name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  release:
    types: [ published ]

env:
  NODE_VERSION: '18'
  CI: true

jobs:
  # Job 1: Code Quality Checks
  quality:
    name: Code Quality
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: TypeScript type checking
      run: npm run typecheck

    - name: Lint code
      run: npm run lint
      continue-on-error: true

    - name: Check code formatting
      run: |
        if command -v prettier &> /dev/null; then
          npx prettier --check "src/**/*.{ts,js,json}"
        else
          echo "Prettier not configured, skipping format check"
        fi
      continue-on-error: true

  # Job 2: Unit Tests
  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest
    needs: quality
    
    strategy:
      matrix:
        node-version: ['18', '20']
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Build project
      run: npm run build

    - name: Run unit tests
      run: npm run test:run -- test/**/*.test.ts !test/integration/** !test/performance/**

    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: unit-test-results-node-${{ matrix.node-version }}
        path: |
          coverage/
          test-results.xml
        retention-days: 7

  # Job 3: Integration Tests
  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: quality
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Build project
      run: npm run build

    - name: Create test translation files
      run: |
        mkdir -p test-locales
        echo '{"common":{"hello":"Hello","goodbye":"Goodbye"}}' > test-locales/en.json
        echo '{"common":{"hello":"Hola","goodbye":"Adiós"}}' > test-locales/es.json
        echo '{"common":{"hello":"Bonjour","goodbye":"Au revoir"}}' > test-locales/fr.json

    - name: Run integration tests
      run: npm run test:run -- test/integration/**/*.test.ts
      env:
        TEST_TRANSLATION_DIR: ./test-locales

    - name: Upload integration test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: integration-test-results
        path: |
          coverage/
          test-results.xml
        retention-days: 7

  # Job 4: Performance Tests
  performance-tests:
    name: Performance Tests
    runs-on: ubuntu-latest
    needs: quality
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Build project
      run: npm run build

    - name: Run performance tests
      run: npm run test:run -- test/performance/**/*.test.ts --reporter=verbose

    - name: Upload performance results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: performance-test-results
        path: |
          coverage/
          performance-results.json
        retention-days: 30

  # Job 5: Coverage Report
  coverage:
    name: Test Coverage
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Build project
      run: npm run build

    - name: Run tests with coverage
      run: npm run test:coverage

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v4
      with:
        file: ./coverage/lcov.info
        flags: unittests
        name: codecov-umbrella
      continue-on-error: true

    - name: Coverage comment on PR
      if: github.event_name == 'pull_request'
      uses: romeovs/lcov-reporter-action@v0.3.1
      with:
        github-token: ${{ secrets.GITHUB_TOKEN }}
        lcov-file: ./coverage/lcov.info
      continue-on-error: true

    - name: Upload coverage artifacts
      uses: actions/upload-artifact@v4
      with:
        name: coverage-report
        path: coverage/
        retention-days: 30

  # Job 6: End-to-End Tests
  e2e-tests:
    name: End-to-End Tests
    runs-on: ${{ matrix.os }}
    needs: [unit-tests, integration-tests]
    
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Build project
      run: npm run build

    - name: Test CLI functionality (Unix)
      if: runner.os != 'Windows'
      run: |
        mkdir -p e2e-test/locales
        echo '{"test":"value"}' > e2e-test/locales/en.json
        timeout 10s node dist/index.js --dir e2e-test/locales --help || true

    - name: Test CLI functionality (Windows)
      if: runner.os == 'Windows'
      run: |
        mkdir e2e-test\locales
        echo {"test":"value"} > e2e-test\locales\en.json
        timeout /t 10 >nul && node dist/index.js --dir e2e-test/locales --help || exit /b 0
      shell: cmd

    - name: Upload E2E test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: e2e-test-results-${{ matrix.os }}
        path: |
          e2e-test/
          *.log
        retention-days: 7

  # Job 7: Security Audit
  security:
    name: Security Audit
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run security audit
      run: npm audit --audit-level=moderate
      continue-on-error: true

    - name: Check for vulnerabilities
      run: |
        if npm audit --audit-level=high --json | jq '.vulnerabilities | length' | grep -q '^0$'; then
          echo "No high-severity vulnerabilities found"
        else
          echo "High-severity vulnerabilities detected"
          npm audit --audit-level=high
          exit 1
        fi
      continue-on-error: true

  # Job 8: Build and Package
  build:
    name: Build and Package
    runs-on: ubuntu-latest
    needs: [quality, unit-tests, integration-tests]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Build project
      run: npm run build

    - name: Create package
      run: npm pack

    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: build-artifacts
        path: |
          dist/
          *.tgz
        retention-days: 30

    - name: Upload package
      uses: actions/upload-artifact@v4
      with:
        name: npm-package
        path: '*.tgz'
        retention-days: 30

  # Job 9: Docker Build (Optional)
  docker:
    name: Docker Build
    runs-on: ubuntu-latest
    needs: build
    if: github.event_name != 'pull_request'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Docker Hub
      if: github.event_name != 'pull_request'
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}
      continue-on-error: true

    - name: Build Docker image
      run: |
        cat > Dockerfile << 'EOF'
        FROM node:18-alpine
        WORKDIR /app
        COPY package*.json ./
        RUN npm ci --only=production
        COPY dist ./dist
        EXPOSE 3000
        CMD ["node", "dist/index.js"]
        EOF

    - name: Build and test Docker image
      run: |
        docker build -t i18n-mcp:latest .
        docker run --rm i18n-mcp:latest --help

  # Job 10: Release
  release:
    name: Release
    runs-on: ubuntu-latest
    needs: [coverage, e2e-tests, build, security]
    if: github.event_name == 'release'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        registry-url: 'https://registry.npmjs.org'

    - name: Install dependencies
      run: npm ci

    - name: Build project
      run: npm run build

    - name: Download build artifacts
      uses: actions/download-artifact@v4
      with:
        name: build-artifacts

    - name: Publish to NPM
      run: npm publish
      env:
        NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
      continue-on-error: true

    - name: Create GitHub Release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: ${{ github.ref }}
        release_name: Release ${{ github.ref }}
        draft: false
        prerelease: false

  # Job 11: Notification
  notify:
    name: Notification
    runs-on: ubuntu-latest
    needs: [coverage, e2e-tests, build]
    if: always()
    
    steps:
    - name: Notify success
      if: needs.coverage.result == 'success' && needs.e2e-tests.result == 'success' && needs.build.result == 'success'
      run: |
        echo "✅ All tests passed successfully!"
        echo "Coverage: ✅ | E2E Tests: ✅ | Build: ✅"

    - name: Notify failure
      if: needs.coverage.result == 'failure' || needs.e2e-tests.result == 'failure' || needs.build.result == 'failure'
      run: |
        echo "❌ Some tests failed!"
        echo "Coverage: ${{ needs.coverage.result }} | E2E Tests: ${{ needs.e2e-tests.result }} | Build: ${{ needs.build.result }}"
        exit 1

    - name: Slack notification (if configured)
      if: always()
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        text: |
          i18n MCP Server CI/CD Pipeline
          Status: ${{ job.status }}
          Commit: ${{ github.sha }}
          Author: ${{ github.actor }}
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK }}
      continue-on-error: true

# Workflow configuration
defaults:
  run:
    shell: bash

# Cache configuration
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true
