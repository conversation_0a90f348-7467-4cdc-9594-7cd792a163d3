import React from 'react';
import { useTranslation } from 'react-i18next';

export function UserDashboard() {
  const { t } = useTranslation();

  const handleSubmit = () => {
    console.log('Form submitted');
  };

  return (
    <div className="dashboard">
      <header>
        <h1>Welcome to your dashboard</h1>
        <p>Manage your account and settings here</p>
      </header>
      
      <main>
        <section>
          <h2>Quick Actions</h2>
          <button onClick={handleSubmit}>
            {t('common.buttons.submit')}
          </button>
          <button>{t('common.buttons.cancel')}</button>
          <button>{t('missing.translation.key')}</button>
        </section>
        
        <section>
          <h3>Account Information</h3>
          <p>Update your profile information below</p>
          <form>
            <label htmlFor="name">Full Name</label>
            <input id="name" type="text" placeholder="Enter your full name" />
            
            <label htmlFor="email">Email Address</label>
            <input id="email" type="email" placeholder="Enter your email" />
            
            <button type="submit">Save Changes</button>
          </form>
        </section>
      </main>
      
      <footer>
        <p>Need help? Contact our support team</p>
      </footer>
    </div>
  );
}