/**
 * Auto-generated translation types
 * Generated at: 2025-06-03T17:49:50.825Z
 * 
 * This file is automatically generated by i18n-mcp.
 * Do not edit manually - changes will be overwritten.
 */

/**
 * All available translation keys
 */
export type TranslationKey =
  | 'common.buttons.save_changes'
  | 'dashboard.account_info_title'
  | 'dashboard.quick_actions_title'
  | 'dashboard.welcome_subtitle'
  | 'dashboard.welcome_title'
  | 'form.full_name_label'
  | 'form.full_name_placeholder'
  | 'missing.translation.key';

/**
 * I18n namespace with translation utilities
 */
export namespace I18n {
  /** Available languages */
  export type Language = 'en' | 'es';
  
  /** Translation key type */
  export type Key = TranslationKey;
  
  
  
  /** Translation function type */
  export type TranslateFunction = (key: Key, params?: Record<string, any>) => string;
  
  /** Translation object type */
  export interface TranslationObject {
    [K in Key]: string;
  }
  
  /** Nested translation structure */
  export type NestedTranslations = {
    [key: string]: string | NestedTranslations;
  };
}

/**
 * Helper types for translation functions
 */
export interface TranslationHelpers {
  /** Check if a key exists */
  hasKey(key: string): key is TranslationKey;
  
  /** Get all keys */
  getKeys(): TranslationKey[];
  
  /** Get available languages */
  getLanguages(): I18n.Language[];
  
  /** Validate translation completeness */
  validateCompleteness(language: I18n.Language): {
    missing: TranslationKey[];
    extra: string[];
  };
}