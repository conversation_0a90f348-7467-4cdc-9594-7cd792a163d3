/**
 * Command line argument parsing utilities
 */
import { ServerConfig } from '../types/translation.js';
/**
 * Parse command line arguments
 */
export declare function parseArgs(): Partial<ServerConfig>;
/**
 * Print help information
 */
export declare function printHelp(): void;
/**
 * Load configuration from environment variables
 */
export declare function loadEnvConfig(): Partial<ServerConfig>;
//# sourceMappingURL=args-parser.d.ts.map