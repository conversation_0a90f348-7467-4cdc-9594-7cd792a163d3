/**
 * Configuration resolution utilities for the CLI
 */
import { ServerConfig } from '../types/translation.js';
/**
 * Default configuration
 */
export declare const DEFAULT_CONFIG: Partial<ServerConfig>;
/**
 * Parse command line arguments
 */
export declare function parseArgs(): Partial<ServerConfig>;
/**
 * Print help information
 */
export declare function printHelp(): void;
/**
 * Load configuration from environment variables
 */
export declare function loadEnvConfig(): Partial<ServerConfig>;
/**
 * Auto-discover translation directory by checking common locations
 */
export declare function discoverTranslationDirectory(): Promise<string | undefined>;
/**
 * Validate and resolve configuration
 */
export declare function resolveConfig(): Promise<ServerConfig>;
//# sourceMappingURL=config-resolver.d.ts.map