/**
 * Configuration resolution utilities for the CLI
 */
import { promises as fs } from 'fs';
import { resolve } from 'path';
/**
 * Default configuration
 */
export const DEFAULT_CONFIG = {
    name: 'i18n-mcp',
    version: '1.0.0',
    baseLanguage: 'en',
    debug: false,
    watchOptions: {
        debounceMs: 100,
        ignored: ['**/node_modules/**', '**/.git/**', '**/dist/**', '**/build/**']
    }
};
/**
 * Parse command line arguments
 */
export function parseArgs() {
    const args = process.argv.slice(2);
    const config = {};
    for (let i = 0; i < args.length; i++) {
        const arg = args[i];
        const nextArg = args[i + 1];
        switch (arg) {
            case '--dir':
            case '-d':
                if (nextArg && !nextArg.startsWith('-')) {
                    config.translationDir = resolve(nextArg);
                    i++;
                }
                break;
            case '--base-language':
            case '-b':
                if (nextArg && !nextArg.startsWith('-')) {
                    config.baseLanguage = nextArg;
                    i++;
                }
                break;
            case '--debug':
                config.debug = true;
                break;
            case '--name':
            case '-n':
                if (nextArg && !nextArg.startsWith('-')) {
                    config.name = nextArg;
                    i++;
                }
                break;
            case '--version':
            case '-v':
                if (nextArg && !nextArg.startsWith('-')) {
                    config.version = nextArg;
                    i++;
                }
                break;
            case '--help':
            case '-h':
                printHelp();
                process.exit(0);
                break;
            default:
                if (arg && arg.startsWith('-')) {
                    console.error(`Unknown option: ${arg}`);
                    printHelp();
                    process.exit(1);
                }
                // Assume it's a directory path if no flag is provided
                if (arg && !config.translationDir) {
                    config.translationDir = resolve(arg);
                }
                break;
        }
    }
    return config;
}
/**
 * Print help information
 */
export function printHelp() {
    console.log(`
i18n MCP Server - High-performance translation file management

Usage: i18n-mcp [options] [translation-directory]

Options:
  -d, --dir <path>           Translation files directory (default: ./locales)
  -b, --base-language <lang> Base language for structure template (default: en)
  -n, --name <name>          Server name (default: i18n-mcp)
  -v, --version <version>    Server version (default: 1.0.0)
  --debug                    Enable debug logging
  -h, --help                 Show this help message

Examples:
  i18n-mcp ./locales                    # Watch ./locales directory
  i18n-mcp --dir ./i18n --debug        # Watch ./i18n with debug logging
  i18n-mcp --base-language fr ./locales # Use French as base language

Environment Variables:
  I18N_MCP_DIR              Translation directory
  I18N_MCP_BASE_LANGUAGE    Base language
  I18N_MCP_DEBUG            Enable debug mode (true/false)
`);
}
/**
 * Load configuration from environment variables
 */
export function loadEnvConfig() {
    const config = {};
    if (process.env.I18N_MCP_DIR) {
        config.translationDir = resolve(process.env.I18N_MCP_DIR);
    }
    if (process.env.I18N_MCP_BASE_LANGUAGE) {
        config.baseLanguage = process.env.I18N_MCP_BASE_LANGUAGE;
    }
    if (process.env.I18N_MCP_DEBUG) {
        config.debug = process.env.I18N_MCP_DEBUG.toLowerCase() === 'true';
    }
    return config;
}
/**
 * Auto-discover translation directory by checking common locations
 */
export async function discoverTranslationDirectory() {
    const commonPaths = [
        'i18n/locales',
        'i18n',
        'locales',
        'translations',
        'lang',
        'languages',
        'src/i18n/locales',
        'src/i18n',
        'src/locales',
        'src/translations',
        'public/locales',
        'assets/i18n',
        'assets/locales'
    ];
    for (const path of commonPaths) {
        const fullPath = resolve(path);
        try {
            await fs.access(fullPath);
            // Check if it contains JSON files
            const files = await fs.readdir(fullPath);
            const hasJsonFiles = files.some(file => file.endsWith('.json'));
            if (hasJsonFiles) {
                console.log(`🔍 Auto-discovered translation directory: ${fullPath}`);
                return fullPath;
            }
        }
        catch {
            // Directory doesn't exist, continue
        }
    }
    return undefined;
}
/**
 * Validate and resolve configuration
 */
export async function resolveConfig() {
    const envConfig = loadEnvConfig();
    const argsConfig = parseArgs();
    const config = {
        ...DEFAULT_CONFIG,
        ...envConfig,
        ...argsConfig
    };
    // Auto-discover translation directory if not provided
    if (!config.translationDir) {
        const discoveredDir = await discoverTranslationDirectory();
        if (discoveredDir) {
            config.translationDir = discoveredDir;
        }
    }
    // Validate that translation directory is provided
    if (!config.translationDir) {
        console.error(`
❌ Translation directory not found!

Please specify the translation directory using one of these methods:
1. Command line argument: i18n-mcp /path/to/translations
2. Environment variable: I18N_MCP_DIR=/path/to/translations
3. Place translation files in a common location like:
   - i18n/locales/
   - i18n/
   - locales/
   - translations/

Example usage:
  i18n-mcp ./i18n/locales
  I18N_MCP_DIR=./locales i18n-mcp
`);
        process.exit(1);
    }
    // Ensure translation directory is absolute
    config.translationDir = resolve(config.translationDir);
    // Validate translation directory exists
    try {
        const stats = await fs.stat(config.translationDir);
        if (!stats.isDirectory()) {
            console.error(`❌ Translation path is not a directory: ${config.translationDir}`);
            process.exit(1);
        }
    }
    catch (error) {
        console.error(`❌ Translation directory does not exist: ${config.translationDir}`);
        console.error('   Create the directory or specify a different path with --dir');
        process.exit(1);
    }
    return config;
}
//# sourceMappingURL=config-resolver.js.map