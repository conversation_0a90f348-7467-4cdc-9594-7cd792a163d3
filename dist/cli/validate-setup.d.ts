#!/usr/bin/env node
/**
 * Setup validation utility for i18n MCP
 */
interface ValidationResult {
    success: boolean;
    issues: string[];
    warnings: string[];
    info: string[];
}
/**
 * Validate translation directory setup
 */
export declare function validateSetup(translationDir?: string): Promise<ValidationResult>;
/**
 * Print validation results
 */
export declare function printValidationResults(result: ValidationResult): void;
export {};
//# sourceMappingURL=validate-setup.d.ts.map