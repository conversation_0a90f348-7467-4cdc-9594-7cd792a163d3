/**
 * File processing utilities for translation files
 */
import { TranslationIndex } from './translation-index.js';
export declare class FileProcessor {
    /**
     * Process a translation file and update the index
     */
    static processTranslationFile(filePath: string, language: string, index: TranslationIndex, debug?: boolean): Promise<void>;
    /**
     * Extract language code from file path
     */
    static extractLanguageFromPath(filePath: string): string;
    /**
     * Clear all translations from a specific file
     */
    private static clearFileFromIndex;
    /**
     * Update index from parsed translation data
     */
    private static updateIndexFromTranslations;
}
//# sourceMappingURL=file-processor.d.ts.map