/**
 * File processing utilities for translation files
 */
import { JsonOperations } from '../utils/json-operations.js';
export class FileProcessor {
    /**
     * Process a translation file and update the index
     */
    static async processTranslationFile(filePath, language, index, debug = false) {
        try {
            const parseResult = await JsonOperations.parseFile(filePath);
            if (debug) {
                console.log(`🔄 Processing ${language}: ${filePath}`);
            }
            // Clear existing translations for this language/file combination
            this.clearFileFromIndex(filePath, language, index);
            // Update index with parsed translations
            await this.updateIndexFromTranslations(parseResult.data, language, filePath, index);
            if (debug) {
                console.log(`✅ Successfully processed ${language} translations from ${filePath}`);
            }
        }
        catch (error) {
            throw new Error(`Failed to process file ${filePath}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
     * Extract language code from file path
     */
    static extractLanguageFromPath(filePath) {
        const basename = filePath.split('/').pop() || '';
        return basename.replace('.json', '');
    }
    /**
     * Clear all translations from a specific file
     */
    static clearFileFromIndex(filePath, language, index) {
        const keysToDelete = [];
        // Find all keys that belong to this file
        for (const keyPath of index.getKeys()) {
            const entry = index.get(keyPath, language);
            if (entry && typeof entry === 'object' && 'file' in entry && entry.file === filePath) {
                keysToDelete.push(keyPath);
            }
        }
        // Delete the keys
        for (const keyPath of keysToDelete) {
            index.delete(keyPath, language);
        }
    }
    /**
     * Update index from parsed translation data
     */
    static async updateIndexFromTranslations(translations, language, filePath, index, prefix = '') {
        if (!translations || typeof translations !== 'object') {
            return;
        }
        for (const [key, value] of Object.entries(translations)) {
            const fullPath = prefix ? `${prefix}.${key}` : key;
            if (value && typeof value === 'object' && !Array.isArray(value)) {
                // Recursively process nested objects
                await this.updateIndexFromTranslations(value, language, filePath, index, fullPath);
            }
            else {
                // Leaf node - add to index
                index.set(fullPath, language, value, {
                    file: filePath,
                    line: 0, // TODO: Could be enhanced to track actual line numbers
                    column: 0
                });
            }
        }
    }
}
//# sourceMappingURL=file-processor.js.map