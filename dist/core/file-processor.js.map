{"version": 3, "file": "file-processor.js", "sourceRoot": "", "sources": ["../../src/core/file-processor.ts"], "names": [], "mappings": "AAAA;;GAEG;AAGH,OAAO,EAAE,cAAc,EAAE,MAAM,6BAA6B,CAAC;AAE7D,MAAM,OAAO,aAAa;IACxB;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,sBAAsB,CACjC,QAAgB,EAChB,QAAgB,EAChB,KAAuB,EACvB,QAAiB,KAAK;QAEtB,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,MAAM,cAAc,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YAE7D,IAAI,KAAK,EAAE,CAAC;gBACV,OAAO,CAAC,GAAG,CAAC,iBAAiB,QAAQ,KAAK,QAAQ,EAAE,CAAC,CAAC;YACxD,CAAC;YAED,iEAAiE;YACjE,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;YAEnD,wCAAwC;YACxC,MAAM,IAAI,CAAC,2BAA2B,CACpC,WAAW,CAAC,IAAI,EAChB,QAAQ,EACR,QAAQ,EACR,KAAK,CACN,CAAC;YAEF,IAAI,KAAK,EAAE,CAAC;gBACV,OAAO,CAAC,GAAG,CAAC,4BAA4B,QAAQ,sBAAsB,QAAQ,EAAE,CAAC,CAAC;YACpF,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,0BAA0B,QAAQ,KAAK,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;QACrH,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,uBAAuB,CAAC,QAAgB;QAC7C,MAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC;QACjD,OAAO,QAAQ,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,kBAAkB,CAAC,QAAgB,EAAE,QAAgB,EAAE,KAAuB;QAC3F,MAAM,YAAY,GAAa,EAAE,CAAC;QAElC,yCAAyC;QACzC,KAAK,MAAM,OAAO,IAAI,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC;YACtC,MAAM,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;YAC3C,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,MAAM,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBACrF,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC7B,CAAC;QACH,CAAC;QAED,kBAAkB;QAClB,KAAK,MAAM,OAAO,IAAI,YAAY,EAAE,CAAC;YACnC,KAAK,CAAC,MAAM,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QAClC,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,KAAK,CAAC,2BAA2B,CAC9C,YAAiB,EACjB,QAAgB,EAChB,QAAgB,EAChB,KAAuB,EACvB,MAAM,GAAG,EAAE;QAEX,IAAI,CAAC,YAAY,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE,CAAC;YACtD,OAAO;QACT,CAAC;QAED,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC;YACxD,MAAM,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;YAEnD,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;gBAChE,qCAAqC;gBACrC,MAAM,IAAI,CAAC,2BAA2B,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;YACrF,CAAC;iBAAM,CAAC;gBACN,2BAA2B;gBAC3B,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE;oBACnC,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,CAAC,EAAE,uDAAuD;oBAChE,MAAM,EAAE,CAAC;iBACV,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;CACF"}