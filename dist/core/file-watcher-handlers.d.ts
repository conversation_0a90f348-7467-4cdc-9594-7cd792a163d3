/**
 * File watcher event handlers and processing logic
 */
import { TranslationIndex } from './translation-index.js';
export declare class FileWatcherHandlers {
    /**
     * Process file changes and update the index
     */
    static processFileChange(filePath: string, eventType: 'add' | 'change', index: TranslationIndex, processedFiles: Set<string>, debounceMs: number, debug: boolean, emit: (event: string, data: any) => void): Promise<void>;
    /**
     * Handle file deletion
     */
    static handleFileDelete(filePath: string, index: TranslationIndex, debug: boolean, emit: (event: string, data: any) => void): void;
    /**
     * Get list of currently watched files from watcher
     */
    static getWatchedFiles(watcher: any): string[];
}
//# sourceMappingURL=file-watcher-handlers.d.ts.map