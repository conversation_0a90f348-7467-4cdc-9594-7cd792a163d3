/**
 * File watcher event handlers and processing logic
 */
import { FileWatchError } from '../types/translation.js';
import { FileProcessor } from './file-processor.js';
export class FileWatcherHandlers {
    /**
     * Process file changes and update the index
     */
    static async processFileChange(filePath, eventType, index, processedFiles, debounceMs, debug, emit) {
        try {
            // Skip if we've already processed this file recently
            if (processedFiles.has(filePath)) {
                return;
            }
            processedFiles.add(filePath);
            // Remove from processed set after a delay to allow for reprocessing
            setTimeout(() => {
                processedFiles.delete(filePath);
            }, debounceMs * 2);
            const language = FileProcessor.extractLanguageFromPath(filePath);
            await FileProcessor.processTranslationFile(filePath, language, index, debug);
            const event = {
                type: eventType,
                path: filePath,
                language,
                timestamp: Date.now()
            };
            emit('fileProcessed', event);
        }
        catch (error) {
            const watchError = new FileWatchError(`Failed to process file ${filePath}`, { path: filePath, error });
            console.error(`❌ ${watchError.message}:`, error);
            emit('error', watchError);
        }
    }
    /**
     * Handle file deletion
     */
    static handleFileDelete(filePath, index, debug, emit) {
        try {
            const language = FileProcessor.extractLanguageFromPath(filePath);
            // Clear all translations from this file
            const keysToDelete = [];
            // Find all keys that belong to this file
            for (const keyPath of index.getKeys()) {
                const entry = index.get(keyPath, language);
                if (entry && typeof entry === 'object' && 'file' in entry && entry.file === filePath) {
                    keysToDelete.push(keyPath);
                }
            }
            // Delete the keys
            for (const keyPath of keysToDelete) {
                index.delete(keyPath, language);
            }
            const event = {
                type: 'unlink',
                path: filePath,
                language,
                timestamp: Date.now()
            };
            emit('fileProcessed', event);
            if (debug) {
                console.log(`🗑️ Removed ${language} translations from index: ${filePath}`);
            }
        }
        catch (error) {
            console.error(`Failed to handle file deletion ${filePath}:`, error);
        }
    }
    /**
     * Get list of currently watched files from watcher
     */
    static getWatchedFiles(watcher) {
        if (!watcher) {
            return [];
        }
        const watched = watcher.getWatched();
        const files = [];
        for (const [dir, filenames] of Object.entries(watched)) {
            if (Array.isArray(filenames)) {
                for (const filename of filenames) {
                    if (filename.endsWith('.json')) {
                        files.push(`${dir}/${filename}`);
                    }
                }
            }
        }
        return files;
    }
}
//# sourceMappingURL=file-watcher-handlers.js.map