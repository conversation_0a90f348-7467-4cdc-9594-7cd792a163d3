{"version": 3, "file": "file-watcher-handlers.js", "sourceRoot": "", "sources": ["../../src/core/file-watcher-handlers.ts"], "names": [], "mappings": "AAAA;;GAEG;AAGH,OAAO,EAAkB,cAAc,EAAE,MAAM,yBAAyB,CAAC;AACzE,OAAO,EAAE,aAAa,EAAE,MAAM,qBAAqB,CAAC;AAEpD,MAAM,OAAO,mBAAmB;IAC9B;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAC5B,QAAgB,EAChB,SAA2B,EAC3B,KAAuB,EACvB,cAA2B,EAC3B,UAAkB,EAClB,KAAc,EACd,IAAwC;QAExC,IAAI,CAAC;YACH,qDAAqD;YACrD,IAAI,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACjC,OAAO;YACT,CAAC;YAED,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAE7B,oEAAoE;YACpE,UAAU,CAAC,GAAG,EAAE;gBACd,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAClC,CAAC,EAAE,UAAU,GAAG,CAAC,CAAC,CAAC;YAEnB,MAAM,QAAQ,GAAG,aAAa,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;YAEjE,MAAM,aAAa,CAAC,sBAAsB,CAAC,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;YAE7E,MAAM,KAAK,GAAmB;gBAC5B,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,QAAQ;gBACd,QAAQ;gBACR,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAC;YAEF,IAAI,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;QAE/B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,UAAU,GAAG,IAAI,cAAc,CACnC,0BAA0B,QAAQ,EAAE,EACpC,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,CAC1B,CAAC;YAEF,OAAO,CAAC,KAAK,CAAC,KAAK,UAAU,CAAC,OAAO,GAAG,EAAE,KAAK,CAAC,CAAC;YACjD,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;QAC5B,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gBAAgB,CACrB,QAAgB,EAChB,KAAuB,EACvB,KAAc,EACd,IAAwC;QAExC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,aAAa,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;YAEjE,wCAAwC;YACxC,MAAM,YAAY,GAAa,EAAE,CAAC;YAElC,yCAAyC;YACzC,KAAK,MAAM,OAAO,IAAI,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC;gBACtC,MAAM,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;gBAC3C,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,MAAM,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;oBACrF,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC7B,CAAC;YACH,CAAC;YAED,kBAAkB;YAClB,KAAK,MAAM,OAAO,IAAI,YAAY,EAAE,CAAC;gBACnC,KAAK,CAAC,MAAM,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;YAClC,CAAC;YAED,MAAM,KAAK,GAAmB;gBAC5B,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,QAAQ;gBACd,QAAQ;gBACR,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAC;YAEF,IAAI,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;YAE7B,IAAI,KAAK,EAAE,CAAC;gBACV,OAAO,CAAC,GAAG,CAAC,eAAe,QAAQ,6BAA6B,QAAQ,EAAE,CAAC,CAAC;YAC9E,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;QACtE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,eAAe,CAAC,OAAY;QACjC,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,MAAM,OAAO,GAAG,OAAO,CAAC,UAAU,EAAE,CAAC;QACrC,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,KAAK,MAAM,CAAC,GAAG,EAAE,SAAS,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;YACvD,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC7B,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;oBACjC,IAAI,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;wBAC/B,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,QAAQ,EAAE,CAAC,CAAC;oBACnC,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;CACF"}