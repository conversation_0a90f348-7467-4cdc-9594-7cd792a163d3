/**
 * Smart file watcher with Chokidar v4 for translation files
 */
import { TranslationIndex } from './translation-index.js';
import { EventEmitter } from 'events';
/**
 * Configuration for the file watcher
 */
export interface FileWatcherConfig {
    /** Directory to watch for translation files */
    translationDir: string;
    /** Debounce delay in milliseconds */
    debounceMs?: number;
    /** File patterns to ignore */
    ignored?: string[];
    /** Enable debug logging */
    debug?: boolean;
}
/**
 * Translation file watcher with optimized change detection
 */
export declare class TranslationFileWatcher extends EventEmitter {
    private readonly index;
    private watcher?;
    private readonly debouncedProcessChange;
    private readonly config;
    private readonly processedFiles;
    constructor(config: FileWatcherConfig, index: TranslationIndex);
    /**
     * Initialize the index by loading all existing translation files
     */
    initializeIndex(): Promise<void>;
    /**
     * Start watching for file changes
     */
    start(): Promise<void>;
    /**
     * Stop watching for file changes
     */
    stop(): Promise<void>;
    /**
     * Get list of currently watched files
     */
    getWatchedFiles(): string[];
    /**
     * Manually trigger processing of a file
     */
    processFile(filePath: string): Promise<void>;
    /**
     * Process file changes and update the index
     */
    private processFileChange;
    /**
     * Handle file deletion
     */
    private handleFileDelete;
    /**
     * Get watcher statistics
     */
    getStats(): {
        isWatching: boolean;
        watchedFiles: number;
        processedFiles: number;
    };
}
//# sourceMappingURL=file-watcher.d.ts.map