/**
 * Batch operations for translation index
 */
import { BatchOperation, IndexedTranslation, ValidationResult, UsageAnalysis } from '../types/translation.js';
export declare class TranslationBatchOperations {
    /**
     * Batch operations for atomic updates
     */
    static batchUpdate(flatIndex: Map<string, IndexedTranslation>, operations: BatchOperation[], emit: (event: string, data: any) => void, invalidateCache: () => void, setMethod: (keyPath: string, language: string, value: any) => void, deleteMethod: (keyPath: string, language?: string) => boolean): Promise<{
        success: boolean;
        errors: string[];
    }>;
    /**
     * Validate structure consistency across languages
     */
    static validateStructure(flatIndex: Map<string, IndexedTranslation>, getKeys: () => string[], baseLanguage: string, autoFix: boolean | undefined, batchUpdateMethod: (operations: BatchOperation[]) => Promise<{
        success: boolean;
        errors: string[];
    }>): Promise<ValidationResult>;
    /**
     * Analyze usage patterns and find optimization opportunities
     */
    static analyzeUsage(flatIndex: Map<string, IndexedTranslation>, getKeys: () => string[], checkDuplicates?: boolean): Promise<UsageAnalysis>;
}
//# sourceMappingURL=translation-batch.d.ts.map