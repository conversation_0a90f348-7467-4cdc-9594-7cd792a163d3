{"version": 3, "file": "translation-batch.js", "sourceRoot": "", "sources": ["../../src/core/translation-batch.ts"], "names": [], "mappings": "AAAA;;GAEG;AAGH,OAAO,EAAE,2BAA2B,EAAE,MAAM,6BAA6B,CAAC;AAE1E,MAAM,OAAO,0BAA0B;IACrC;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,WAAW,CACtB,SAA0C,EAC1C,UAA4B,EAC5B,IAAwC,EACxC,eAA2B,EAC3B,SAAkE,EAClE,YAA6D;QAE7D,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,SAAS,CAAC,CAAC;QAElC,IAAI,CAAC;YACH,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;gBACnC,IAAI,CAAC;oBACH,IAAI,SAAS,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC;wBAC7B,IAAI,CAAC,SAAS,CAAC,QAAQ,IAAI,SAAS,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;4BACzD,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;wBAC/D,CAAC;wBACD,SAAS,CAAC,SAAS,CAAC,OAAO,EAAE,SAAS,CAAC,QAAQ,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC;oBACpE,CAAC;yBAAM,IAAI,SAAS,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;wBACvC,YAAY,CAAC,SAAS,CAAC,OAAO,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC;oBACtD,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,MAAM,CAAC,IAAI,CAAC,wBAAwB,SAAS,CAAC,OAAO,KAAK,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;gBACxH,CAAC;YACH,CAAC;YAED,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACtB,yBAAyB;gBACzB,SAAS,CAAC,KAAK,EAAE,CAAC;gBAClB,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;gBAC1D,eAAe,EAAE,CAAC;gBAElB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;YACpC,CAAC;YAED,eAAe,EAAE,CAAC;YAClB,IAAI,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;YAEhC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;QAEvC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,oBAAoB;YACpB,SAAS,CAAC,KAAK,EAAE,CAAC;YAClB,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;YAC1D,eAAe,EAAE,CAAC;YAElB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,CAAC,2BAA2B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC;aAChG,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAC5B,SAA0C,EAC1C,OAAuB,EACvB,YAAoB,EACpB,UAAmB,KAAK,EACxB,iBAAoG;QAEpG,MAAM,MAAM,GAAG,MAAM,2BAA2B,CAAC,iBAAiB,CAChE,SAAS,EACT,OAAO,EAAE,EACT,YAAY,EACZ,OAAO,CACR,CAAC;QAEF,6CAA6C;QAC7C,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;YAC7B,MAAM,aAAa,GAAqB,EAAE,CAAC;YAE3C,2CAA2C;YAC3C,KAAK,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC;gBACzE,KAAK,MAAM,OAAO,IAAI,WAAW,EAAE,CAAC;oBAClC,MAAM,SAAS,GAAG,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;oBACzC,IAAI,SAAS,IAAI,SAAS,CAAC,YAAY,CAAC,EAAE,CAAC;wBACzC,aAAa,CAAC,IAAI,CAAC;4BACjB,IAAI,EAAE,KAAK;4BACX,OAAO;4BACP,QAAQ;4BACR,KAAK,EAAE,aAAa,SAAS,CAAC,YAAY,CAAC,CAAC,KAAK,GAAG;yBACrD,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;YACH,CAAC;YAED,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC7B,MAAM,iBAAiB,CAAC,aAAa,CAAC,CAAC;gBACvC,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,cAAc,aAAa,CAAC,MAAM,uBAAuB,CAAC,CAAC;YAC1F,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,YAAY,CACvB,SAA0C,EAC1C,OAAuB,EACvB,kBAA2B,IAAI;QAE/B,OAAO,2BAA2B,CAAC,YAAY,CAAC,SAAS,EAAE,OAAO,EAAE,EAAE,eAAe,CAAC,CAAC;IACzF,CAAC;CACF"}