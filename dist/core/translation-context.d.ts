/**
 * Translation context retrieval functionality
 */
import { IndexedTranslation, ContextOptions, ContextResult } from '../types/translation.js';
export declare class TranslationContextEngine {
    /**
     * Get translation context with hierarchical information
     */
    static getContext(flatIndex: Map<string, IndexedTranslation>, sortedKeys: string[], keyPath: string, options: ContextOptions): Promise<ContextResult | null>;
}
//# sourceMappingURL=translation-context.d.ts.map