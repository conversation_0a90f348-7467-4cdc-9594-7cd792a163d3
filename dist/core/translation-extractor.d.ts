/**
 * Translation extraction and replacement utilities
 */
import { SupportedFramework } from '../types/translation.js';
/**
 * Translation extractor for replacing hardcoded strings with translation calls
 */
export declare class TranslationExtractor {
    private framework?;
    constructor(framework?: SupportedFramework);
    /**
     * Replace hardcoded text with translation call in a file
     */
    replaceTextWithTranslation(filePath: string, originalText: string, translationKey: string): Promise<string>;
    /**
     * Replace hardcoded text with translation call in content string
     */
    replaceInContent(content: string, originalText: string, translationKey: string): string;
    /**
     * Get the replacement pattern for a given framework and key
     */
    getReplacementPattern(translationKey: string, framework?: SupportedFramework): string;
    /**
     * Replace strings in React/JSX content
     */
    private replaceReactStrings;
    /**
     * Replace strings in Vue template content
     */
    private replaceVueStrings;
    /**
     * Replace strings in Svelte content
     */
    private replaceSvelteStrings;
    /**
     * Replace strings in Angular template content
     */
    private replaceAngularStrings;
    /**
     * Ensure the necessary import statement is present
     */
    private ensureImport;
    /**
     * Escape special regex characters
     */
    private escapeRegex;
    /**
     * Generate smart translation key based on context
     */
    generateSmartKey(text: string, context?: string, keyStyle?: 'nested' | 'flat' | 'camelCase' | 'kebab-case'): string;
    /**
     * Extract meaningful prefix from context (file path or component name)
     */
    private extractContextPrefix;
}
/**
 * Utility functions for translation extraction
 */
export declare class ExtractionUtils {
    /**
     * Find similar existing translations to avoid duplicates
     */
    static findSimilarTranslations(text: string, translationIndex: any, threshold?: number): Promise<Array<{
        keyPath: string;
        value: string;
        score: number;
    }>>;
    /**
     * Validate translation key format
     */
    static validateKeyFormat(key: string, keyStyle: string): boolean;
}
//# sourceMappingURL=translation-extractor.d.ts.map