/**
 * Translation search functionality
 */
import { IndexedTranslation, SearchOptions, SearchResult } from '../types/translation.js';
export declare class TranslationSearchEngine {
    /**
     * Search translations with advanced options
     */
    static search(flatIndex: Map<string, IndexedTranslation>, sortedKeys: string[], query: string, options?: SearchOptions): Promise<SearchResult[]>;
    /**
     * Optimized prefix search using binary search
     */
    static searchByPrefix(sortedKeys: string[], prefix: string): string[];
    private static calculateKeyScore;
    private static calculateValueScore;
    private static binarySearchStart;
    private static binarySearchEnd;
}
//# sourceMappingURL=translation-search.d.ts.map