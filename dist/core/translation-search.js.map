{"version": 3, "file": "translation-search.js", "sourceRoot": "", "sources": ["../../src/core/translation-search.ts"], "names": [], "mappings": "AAAA;;GAEG;AAQH,MAAM,OAAO,uBAAuB;IAClC;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,MAAM,CACjB,SAA0C,EAC1C,UAAoB,EACpB,KAAa,EACb,UAAyB,EAAE,KAAK,EAAE,MAAM,EAAE;QAE1C,MAAM,OAAO,GAAmB,EAAE,CAAC;QACnC,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;QACvC,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,KAAK,CAAC;QACrD,MAAM,WAAW,GAAG,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC;QAEvD,KAAK,MAAM,OAAO,IAAI,UAAU,EAAE,CAAC;YACjC,MAAM,KAAK,GAAG,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YACrC,IAAI,CAAC,KAAK;gBAAE,SAAS;YAErB,IAAI,SAAS,GAAoC,IAAI,CAAC;YACtD,IAAI,KAAK,GAAG,CAAC,CAAC;YAEd,kBAAkB;YAClB,MAAM,UAAU,GAAG,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;YACnE,MAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,KAAK,MAAM,IAAI,OAAO,CAAC,KAAK,KAAK,MAAM,CAAC;YACtE,IAAI,QAAQ,IAAI,UAAU,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;gBACjD,SAAS,GAAG,KAAK,CAAC;gBAClB,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;YAC1D,CAAC;YAED,oBAAoB;YACpB,MAAM,UAAU,GAAG,OAAO,CAAC,KAAK,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,KAAK,MAAM,CAAC;YAC1E,IAAI,UAAU,EAAE,CAAC;gBACf,KAAK,MAAM,CAAC,IAAI,EAAE,gBAAgB,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC7D,IAAI,OAAO,CAAC,SAAS,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;wBAC3D,SAAS;oBACX,CAAC;oBAED,MAAM,QAAQ,GAAG,MAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;oBAChD,MAAM,YAAY,GAAG,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;oBAEvE,IAAI,YAAY,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;wBACvC,MAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;wBACvE,IAAI,UAAU,GAAG,KAAK,EAAE,CAAC;4BACvB,KAAK,GAAG,UAAU,CAAC;4BACnB,SAAS,GAAG,SAAS,KAAK,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC;wBACrD,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;YAED,IAAI,SAAS,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;gBAC3B,mCAAmC;gBACnC,MAAM,aAAa,GAAuB,EAAE,CAAC;gBAC7C,KAAK,MAAM,CAAC,IAAI,EAAE,gBAAgB,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC7D,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;wBAC3D,aAAa,CAAC,IAAI,CAAC,GAAG,gBAAgB,CAAC;oBACzC,CAAC;gBACH,CAAC;gBAED,IAAI,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC1C,OAAO,CAAC,IAAI,CAAC;wBACX,OAAO;wBACP,YAAY,EAAE,aAAa;wBAC3B,KAAK;wBACL,SAAS;qBACV,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAED,gBAAgB;YAChB,IAAI,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;gBAC/D,MAAM;YACR,CAAC;QACH,CAAC;QAED,6BAA6B;QAC7B,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;QAE1C,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,cAAc,CAAC,UAAoB,EAAE,MAAc;QACxD,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QAC5D,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QAExD,OAAO,UAAU,CAAC,KAAK,CAAC,QAAQ,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;IAChD,CAAC;IAEO,MAAM,CAAC,iBAAiB,CAAC,GAAW,EAAE,KAAa;QACzD,IAAI,GAAG,KAAK,KAAK;YAAE,OAAO,GAAG,CAAC;QAC9B,IAAI,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC;YAAE,OAAO,GAAG,CAAC;QACtC,IAAI,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC;YAAE,OAAO,GAAG,CAAC;QACpC,OAAO,GAAG,CAAC;IACb,CAAC;IAEO,MAAM,CAAC,mBAAmB,CAAC,KAAa,EAAE,KAAa;QAC7D,IAAI,KAAK,KAAK,KAAK;YAAE,OAAO,GAAG,CAAC;QAChC,IAAI,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC;YAAE,OAAO,GAAG,CAAC;QACxC,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC;YAAE,OAAO,GAAG,CAAC;QACtC,OAAO,GAAG,CAAC;IACb,CAAC;IAEO,MAAM,CAAC,iBAAiB,CAAC,UAAoB,EAAE,MAAc;QACnE,IAAI,IAAI,GAAG,CAAC,CAAC;QACb,IAAI,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC;QAE9B,OAAO,IAAI,GAAG,KAAK,EAAE,CAAC;YACpB,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;YAC3C,MAAM,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;YAC5B,IAAI,GAAG,IAAI,GAAG,GAAG,MAAM,EAAE,CAAC;gBACxB,IAAI,GAAG,GAAG,GAAG,CAAC,CAAC;YACjB,CAAC;iBAAM,CAAC;gBACN,KAAK,GAAG,GAAG,CAAC;YACd,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,MAAM,CAAC,eAAe,CAAC,UAAoB,EAAE,MAAc;QACjE,IAAI,IAAI,GAAG,CAAC,CAAC;QACb,IAAI,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC;QAC9B,MAAM,SAAS,GAAG,MAAM,GAAG,QAAQ,CAAC,CAAC,wBAAwB;QAE7D,OAAO,IAAI,GAAG,KAAK,EAAE,CAAC;YACpB,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;YAC3C,MAAM,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;YAC5B,IAAI,GAAG,IAAI,GAAG,GAAG,SAAS,EAAE,CAAC;gBAC3B,IAAI,GAAG,GAAG,GAAG,CAAC,CAAC;YACjB,CAAC;iBAAM,CAAC;gBACN,KAAK,GAAG,GAAG,CAAC;YACd,CAAC;QACH,CAAC;QAED,OAAO,IAAI,GAAG,CAAC,CAAC;IAClB,CAAC;CACF"}