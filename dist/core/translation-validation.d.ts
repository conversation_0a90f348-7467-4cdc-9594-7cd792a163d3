/**
 * Translation validation and analysis functionality
 */
import { IndexedTranslation, ValidationResult, UsageAnalysis } from '../types/translation.js';
export declare class TranslationValidationEngine {
    /**
     * Validate structure consistency across languages
     */
    static validateStructure(flatIndex: Map<string, IndexedTranslation>, allKeys: string[], baseLanguage: string, autoFix?: boolean): Promise<ValidationResult>;
    /**
     * Analyze usage patterns and find optimization opportunities
     */
    static analyzeUsage(flatIndex: Map<string, IndexedTranslation>, allKeys: string[], checkDuplicates?: boolean): Promise<UsageAnalysis>;
    private static getLanguages;
}
//# sourceMappingURL=translation-validation.d.ts.map