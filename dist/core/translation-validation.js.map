{"version": 3, "file": "translation-validation.js", "sourceRoot": "", "sources": ["../../src/core/translation-validation.ts"], "names": [], "mappings": "AAAA;;GAEG;AASH,MAAM,OAAO,2BAA2B;IACtC;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAC5B,SAA0C,EAC1C,OAAiB,EACjB,YAAoB,EACpB,UAAmB,KAAK;QAExB,MAAM,MAAM,GAAqB;YAC/B,KAAK,EAAE,IAAI;YACX,WAAW,EAAE,EAAE;YACf,SAAS,EAAE,EAAE;YACb,cAAc,EAAE,EAAE;YAClB,gBAAgB,EAAE,EAAE;SACrB,CAAC;QAEF,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QAE/C,yCAAyC;QACzC,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAU,CAAC;QACnC,KAAK,MAAM,OAAO,IAAI,OAAO,EAAE,CAAC;YAC9B,MAAM,KAAK,GAAG,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YACrC,IAAI,KAAK,IAAI,KAAK,CAAC,YAAY,CAAC,EAAE,CAAC;gBACjC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YACxB,CAAC;QACH,CAAC;QAED,uCAAuC;QACvC,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YACjC,IAAI,QAAQ,KAAK,YAAY;gBAAE,SAAS;YAExC,MAAM,YAAY,GAAG,IAAI,GAAG,EAAU,CAAC;YACvC,MAAM,WAAW,GAAa,EAAE,CAAC;YACjC,MAAM,SAAS,GAAa,EAAE,CAAC;YAE/B,iCAAiC;YACjC,KAAK,MAAM,OAAO,IAAI,OAAO,EAAE,CAAC;gBAC9B,MAAM,KAAK,GAAG,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBACrC,IAAI,KAAK,IAAI,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC7B,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBAC5B,CAAC;YACH,CAAC;YAED,kDAAkD;YAClD,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;gBAC/B,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;oBAC/B,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBAC1B,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;gBACvB,CAAC;YACH,CAAC;YAED,gDAAgD;YAChD,KAAK,MAAM,OAAO,IAAI,YAAY,EAAE,CAAC;gBACnC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;oBAC3B,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBACxB,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;gBACvB,CAAC;YACH,CAAC;YAED,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC3B,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,WAAW,CAAC;YAC7C,CAAC;YAED,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACzB,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,SAAS,CAAC;YACzC,CAAC;YAED,yBAAyB;YACzB,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;gBAC/B,MAAM,SAAS,GAAG,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBACzC,MAAM,SAAS,GAAG,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBAEzC,IAAI,SAAS,IAAI,SAAS,IAAI,SAAS,CAAC,YAAY,CAAC,IAAI,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC7E,MAAM,QAAQ,GAAG,OAAO,SAAS,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC;oBACtD,MAAM,QAAQ,GAAG,OAAO,SAAS,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC;oBAElD,IAAI,QAAQ,KAAK,QAAQ,EAAE,CAAC;wBAC1B,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC;4BACzB,OAAO;4BACP,QAAQ,EAAE,QAAQ;4BAClB,MAAM,EAAE,EAAE,CAAC,QAAQ,CAAC,EAAE,QAAQ,EAAE;yBACjC,CAAC,CAAC;wBACH,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;oBACvB,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,YAAY,CACvB,SAA0C,EAC1C,OAAiB,EACjB,kBAA2B,IAAI;QAE/B,MAAM,MAAM,GAAkB;YAC5B,SAAS,EAAE,SAAS,CAAC,IAAI;YACzB,UAAU,EAAE,EAAE,EAAE,4CAA4C;YAC5D,eAAe,EAAE,EAAE;YACnB,mBAAmB,EAAE,EAAE;YACvB,aAAa,EAAE,EAAE;SAClB,CAAC;QAEF,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QAE/C,gCAAgC;QAChC,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YACjC,IAAI,cAAc,GAAG,CAAC,CAAC;YAEvB,KAAK,MAAM,OAAO,IAAI,OAAO,EAAE,CAAC;gBAC9B,MAAM,KAAK,GAAG,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBACrC,IAAI,KAAK,IAAI,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC7B,cAAc,EAAE,CAAC;gBACnB,CAAC;YACH,CAAC;YAED,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG;gBAC/B,SAAS,EAAE,OAAO,CAAC,MAAM;gBACzB,cAAc;gBACd,YAAY,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;aACvE,CAAC;QACJ,CAAC;QAED,4BAA4B;QAC5B,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YACjC,MAAM,WAAW,GAAa,EAAE,CAAC;YAEjC,KAAK,MAAM,OAAO,IAAI,OAAO,EAAE,CAAC;gBAC9B,MAAM,KAAK,GAAG,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBACrC,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC/B,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC5B,CAAC;YACH,CAAC;YAED,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC3B,MAAM,CAAC,mBAAmB,CAAC,QAAQ,CAAC,GAAG,WAAW,CAAC;YACrD,CAAC;QACH,CAAC;QAED,wBAAwB;QACxB,IAAI,eAAe,EAAE,CAAC;YACpB,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAoB,CAAC;YAE7C,KAAK,MAAM,OAAO,IAAI,OAAO,EAAE,CAAC;gBAC9B,MAAM,KAAK,GAAG,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBACrC,IAAI,KAAK,EAAE,CAAC;oBACV,KAAK,MAAM,CAAC,QAAQ,EAAE,gBAAgB,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;wBACjE,MAAM,QAAQ,GAAG,MAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;wBAChD,MAAM,GAAG,GAAG,GAAG,QAAQ,IAAI,QAAQ,EAAE,CAAC;wBAEtC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;4BACvB,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;wBACxB,CAAC;wBACD,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBACnC,CAAC;gBACH,CAAC;YACH,CAAC;YAED,KAAK,MAAM,CAAC,GAAG,EAAE,QAAQ,CAAC,IAAI,QAAQ,EAAE,CAAC;gBACvC,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACxB,MAAM,CAAC,EAAE,KAAK,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;oBACpC,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC;wBAC1B,KAAK;wBACL,IAAI,EAAE,QAAQ;qBACf,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,MAAM,CAAC,YAAY,CAAC,SAA0C;QACpE,MAAM,SAAS,GAAG,IAAI,GAAG,EAAU,CAAC;QAEpC,KAAK,MAAM,KAAK,IAAI,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC;YACvC,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;gBACtC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACtB,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,EAAE,CAAC;IACtC,CAAC;CACF"}