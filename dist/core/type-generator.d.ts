/**
 * TypeScript type generation for translation keys
 */
import { TranslationIndex } from './translation-index.js';
/**
 * Options for type generation
 */
export interface TypeGenerationOptions {
    /** Output file path */
    outputPath: string;
    /** Namespace for the types */
    namespace?: string;
    /** Include value types */
    includeValues?: boolean;
    /** Generate strict types (literal unions) */
    strict?: boolean;
    /** Base language for type inference */
    baseLanguage?: string;
}
/**
 * TypeScript type generator for translation keys
 */
export declare class TypeGenerator {
    private index;
    constructor(index: TranslationIndex);
    /**
     * Generate TypeScript types for translation keys
     */
    generateTypes(options: TypeGenerationOptions): Promise<void>;
    /**
     * Build the complete type definition content
     */
    private buildTypeDefinitions;
    /**
     * Generate file header with metadata
     */
    private generateHeader;
    /**
     * Generate translation key types
     */
    private generateKeyTypes;
    /**
     * Generate value types based on actual translation values
     */
    private generateValueTypes;
    /**
     * Generate namespace declaration
     */
    private generateNamespace;
    /**
     * Infer TypeScript type from value
     */
    private inferValueType;
    /**
     * Generate nested type structure from translation keys
     */
    private generateNestedTypes;
    /**
     * Build a tree structure from flat keys
     */
    private buildKeyTree;
    /**
     * Convert tree structure to TypeScript interface
     */
    private treeToTypeScript;
    /**
     * Watch for changes and regenerate types
     */
    watchAndRegenerate(options: TypeGenerationOptions): Promise<void>;
}
/**
 * Utility functions for type generation
 */
export declare class TypeGenerationUtils {
    /**
     * Validate generated types by attempting to compile them
     */
    static validateTypes(filePath: string): Promise<{
        valid: boolean;
        errors: string[];
    }>;
    /**
     * Generate JSDoc comments for translation keys
     */
    static generateKeyDocumentation(keys: string[], index: TranslationIndex, baseLanguage?: string): Record<string, string>;
}
//# sourceMappingURL=type-generator.d.ts.map