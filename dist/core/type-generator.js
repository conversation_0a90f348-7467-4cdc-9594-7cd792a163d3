/**
 * TypeScript type generation for translation keys
 */
import { promises as fs } from 'fs';
import { dirname } from 'path';
/**
 * TypeScript type generator for translation keys
 */
export class TypeGenerator {
    index;
    constructor(index) {
        this.index = index;
    }
    /**
     * Generate TypeScript types for translation keys
     */
    async generateTypes(options) {
        const { outputPath, namespace = 'I18n', includeValues = false, strict = true, baseLanguage = 'en' } = options;
        try {
            // Ensure output directory exists
            await fs.mkdir(dirname(outputPath), { recursive: true });
            const typeContent = this.buildTypeDefinitions(namespace, includeValues, strict, baseLanguage);
            await fs.writeFile(outputPath, typeContent, 'utf-8');
        }
        catch (error) {
            throw new Error(`Failed to generate types: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
     * Build the complete type definition content
     */
    buildTypeDefinitions(namespace, includeValues, strict, baseLanguage) {
        const keys = this.index.getKeys();
        const languages = this.index.getLanguages();
        const header = this.generateHeader();
        const keyTypes = this.generateKeyTypes(keys, strict);
        const valueTypes = includeValues ? this.generateValueTypes(keys, baseLanguage) : '';
        const namespaceDeclaration = this.generateNamespace(namespace, keyTypes, valueTypes, languages, strict);
        return [header, keyTypes, valueTypes, namespaceDeclaration].filter(Boolean).join('\n\n');
    }
    /**
     * Generate file header with metadata
     */
    generateHeader() {
        const timestamp = new Date().toISOString();
        return `/**
 * Auto-generated translation types
 * Generated at: ${timestamp}
 * 
 * This file is automatically generated by i18n-mcp.
 * Do not edit manually - changes will be overwritten.
 */`;
    }
    /**
     * Generate translation key types
     */
    generateKeyTypes(keys, strict) {
        if (strict) {
            // Generate literal union type
            const keyLiterals = keys.map(key => `  | '${key}'`).join('\n');
            return `/**
 * All available translation keys
 */
export type TranslationKey =
${keyLiterals};`;
        }
        else {
            // Generate string type with autocomplete hints
            const keyHints = keys.slice(0, 20).map(key => `  | '${key}'`).join('\n');
            return `/**
 * Translation keys (with autocomplete hints)
 */
export type TranslationKey = string & {
  // Autocomplete hints:
${keyHints}
  | (string & {});
};`;
        }
    }
    /**
     * Generate value types based on actual translation values
     */
    generateValueTypes(keys, baseLanguage) {
        const valueTypes = [];
        // Analyze value types
        const typeMap = new Map();
        keys.forEach(key => {
            const entry = this.index.get(key, baseLanguage);
            if (entry && typeof entry === 'object' && 'value' in entry) {
                const valueType = this.inferValueType(entry.value);
                if (!typeMap.has(key)) {
                    typeMap.set(key, new Set());
                }
                typeMap.get(key).add(valueType);
            }
        });
        // Generate interface for translation values
        const valueInterface = Array.from(typeMap.entries())
            .map(([key, types]) => {
            const unionType = Array.from(types).join(' | ');
            return `  '${key}': ${unionType};`;
        })
            .join('\n');
        return `/**
 * Translation value types
 */
export interface TranslationValues {
${valueInterface}
}`;
    }
    /**
     * Generate namespace declaration
     */
    generateNamespace(namespace, keyTypes, valueTypes, languages, strict) {
        const languageUnion = languages.map(lang => `'${lang}'`).join(' | ');
        return `/**
 * ${namespace} namespace with translation utilities
 */
export namespace ${namespace} {
  /** Available languages */
  export type Language = ${languageUnion};
  
  /** Translation key type */
  export type Key = TranslationKey;
  
  ${valueTypes ? '/** Translation value types */\n  export type Values = TranslationValues;\n' : ''}
  
  /** Translation function type */
  export type TranslateFunction = (key: Key, params?: Record<string, any>) => string;
  
  /** Translation object type */
  export interface TranslationObject {
    [K in Key]: string;
  }
  
  /** Nested translation structure */
  export type NestedTranslations = {
    [key: string]: string | NestedTranslations;
  };
}

/**
 * Helper types for translation functions
 */
export interface TranslationHelpers {
  /** Check if a key exists */
  hasKey(key: string): key is TranslationKey;
  
  /** Get all keys */
  getKeys(): TranslationKey[];
  
  /** Get available languages */
  getLanguages(): ${namespace}.Language[];
  
  /** Validate translation completeness */
  validateCompleteness(language: ${namespace}.Language): {
    missing: TranslationKey[];
    extra: string[];
  };
}`;
    }
    /**
     * Infer TypeScript type from value
     */
    inferValueType(value) {
        if (typeof value === 'string') {
            // Check for interpolation patterns
            if (value.includes('{{') || value.includes('{') || value.includes('%')) {
                return 'string'; // Parameterized string
            }
            return 'string';
        }
        else if (typeof value === 'number') {
            return 'number';
        }
        else if (typeof value === 'boolean') {
            return 'boolean';
        }
        else if (Array.isArray(value)) {
            const elementTypes = value.map(v => this.inferValueType(v));
            const uniqueTypes = [...new Set(elementTypes)];
            return `(${uniqueTypes.join(' | ')})[]`;
        }
        else if (typeof value === 'object' && value !== null) {
            return 'Record<string, any>';
        }
        return 'any';
    }
    /**
     * Generate nested type structure from translation keys
     */
    generateNestedTypes(keys) {
        const tree = this.buildKeyTree(keys);
        return this.treeToTypeScript(tree, 'TranslationTree');
    }
    /**
     * Build a tree structure from flat keys
     */
    buildKeyTree(keys) {
        const tree = {};
        keys.forEach(key => {
            const parts = key.split('.');
            let current = tree;
            parts.forEach((part, index) => {
                if (index === parts.length - 1) {
                    current[part] = 'string';
                }
                else {
                    if (!current[part]) {
                        current[part] = {};
                    }
                    current = current[part];
                }
            });
        });
        return tree;
    }
    /**
     * Convert tree structure to TypeScript interface
     */
    treeToTypeScript(tree, typeName, depth = 0) {
        const indent = '  '.repeat(depth);
        const properties = [];
        Object.entries(tree).forEach(([key, value]) => {
            if (typeof value === 'string') {
                properties.push(`${indent}  ${key}: string;`);
            }
            else {
                const nestedTypeName = `${typeName}_${key}`;
                const nestedType = this.treeToTypeScript(value, nestedTypeName, depth + 1);
                properties.push(`${indent}  ${key}: ${nestedTypeName};`);
            }
        });
        return `${indent}interface ${typeName} {
${properties.join('\n')}
${indent}}`;
    }
    /**
     * Watch for changes and regenerate types
     */
    async watchAndRegenerate(options) {
        // Set up file watcher for translation files
        this.index.on('set', () => {
            this.generateTypes(options).catch(error => {
                console.error('Failed to regenerate types:', error);
            });
        });
        this.index.on('delete', () => {
            this.generateTypes(options).catch(error => {
                console.error('Failed to regenerate types:', error);
            });
        });
    }
}
/**
 * Utility functions for type generation
 */
export class TypeGenerationUtils {
    /**
     * Validate generated types by attempting to compile them
     */
    static async validateTypes(filePath) {
        try {
            const content = await fs.readFile(filePath, 'utf-8');
            // Basic syntax validation
            const errors = [];
            // Check for common syntax errors
            if (!content.includes('export type TranslationKey')) {
                errors.push('Missing TranslationKey type export');
            }
            // Check for balanced braces
            const openBraces = (content.match(/{/g) || []).length;
            const closeBraces = (content.match(/}/g) || []).length;
            if (openBraces !== closeBraces) {
                errors.push('Unbalanced braces in generated types');
            }
            return {
                valid: errors.length === 0,
                errors
            };
        }
        catch (error) {
            return {
                valid: false,
                errors: [`Failed to validate types: ${error instanceof Error ? error.message : 'Unknown error'}`]
            };
        }
    }
    /**
     * Generate JSDoc comments for translation keys
     */
    static generateKeyDocumentation(keys, index, baseLanguage = 'en') {
        const docs = {};
        keys.forEach(key => {
            const entry = index.get(key, baseLanguage);
            if (entry && typeof entry === 'object' && 'value' in entry) {
                const value = entry.value;
                if (typeof value === 'string' && value.length < 100) {
                    docs[key] = `/** ${value} */`;
                }
                else {
                    docs[key] = `/** Translation key: ${key} */`;
                }
            }
        });
        return docs;
    }
}
//# sourceMappingURL=type-generator.js.map