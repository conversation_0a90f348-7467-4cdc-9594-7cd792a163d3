{"version": 3, "file": "mcp-server.js", "sourceRoot": "", "sources": ["../../src/server/mcp-server.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,EAAE,SAAS,EAAE,MAAM,yCAAyC,CAAC;AACpE,OAAO,EAAE,oBAAoB,EAAE,MAAM,2CAA2C,CAAC;AACjF,OAAO,EAAE,gBAAgB,EAAE,MAAM,8BAA8B,CAAC;AAChE,OAAO,EAAE,sBAAsB,EAAE,MAAM,yBAAyB,CAAC;AACjE,OAAO,EAAE,aAAa,EAAE,MAAM,2BAA2B,CAAC;AAE1D,OAAO,EAAE,QAAQ,EAAE,MAAM,gBAAgB,CAAC;AAE1C;;GAEG;AACH,MAAM,OAAO,oBAAoB;IACd,MAAM,CAAY;IAClB,KAAK,CAAmB;IACxB,WAAW,CAAyB;IACpC,MAAM,CAAyB;IAEhD,YAAY,MAAoB;QAC9B,IAAI,CAAC,MAAM,GAAG;YACZ,YAAY,EAAE,IAAI;YAClB,KAAK,EAAE,KAAK;YACZ,YAAY,EAAE;gBACZ,UAAU,EAAE,GAAG;gBACf,OAAO,EAAE,CAAC,oBAAoB,EAAE,YAAY,CAAC;aAC9C;YACD,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,EAAE;YACX,QAAQ,EAAE,KAAK;YACf,aAAa,EAAE,SAAS;YACxB,SAAS,EAAE,KAAK;YAChB,WAAW,EAAE,OAAO,CAAC,GAAG,EAAE;YAC1B,UAAU,EAAE,EAAE;YACd,QAAQ,EAAE,QAAQ;YAClB,GAAG,MAAM;SACgB,CAAC;QAE5B,6BAA6B;QAC7B,IAAI,CAAC,KAAK,GAAG,IAAI,gBAAgB,CAAC;YAChC,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY;YACtC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK;SACzB,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,GAAG,IAAI,sBAAsB,CAC3C;YACE,cAAc,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc;YAC1C,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,UAAU;YAC/C,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO;YACzC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK;SACzB,EACD,IAAI,CAAC,KAAK,CACX,CAAC;QAEF,wBAAwB;QACxB,IAAI,CAAC,MAAM,GAAG,IAAI,SAAS,CAAC;YAC1B,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;YACtB,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;SAC7B,EAAE;YACD,YAAY,EAAE;gBACZ,KAAK,EAAE,EAAE;gBACT,SAAS,EAAE,EAAE;gBACb,OAAO,EAAE,EAAE;aACZ;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE1B,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;YACtB,OAAO,CAAC,GAAG,CAAC,wCAAwC,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;QAClG,CAAC;IACH,CAAC;IAED;;OAEG;IACK,UAAU;QAChB,MAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAEzE,qDAAqD;QACrD,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAEpC,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;YACtB,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,kBAAkB;QACxB,eAAe;QACf,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;YAC7B,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;gBACtB,OAAO,CAAC,GAAG,CAAC,uBAAuB,KAAK,CAAC,OAAO,KAAK,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC;YAC1E,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,EAAE;YAChC,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;gBACtB,OAAO,CAAC,GAAG,CAAC,4BAA4B,KAAK,CAAC,OAAO,KAAK,KAAK,CAAC,QAAQ,IAAI,KAAK,GAAG,CAAC,CAAC;YACxF,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,sBAAsB;QACtB,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,eAAe,EAAE,CAAC,KAAK,EAAE,EAAE;YAC7C,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;gBACtB,OAAO,CAAC,GAAG,CAAC,sBAAsB,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC;YACpF,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;YACrC,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;YAChC,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;gBACtB,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;YACvC,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK;QACT,IAAI,CAAC;YACH,gCAAgC;YAChC,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;YAE/B,6BAA6B;YAC7B,MAAM,cAAc,GAAG,IAAI,oBAAoB,EAAE,CAAC;YAClD,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YAE1C,OAAO,CAAC,GAAG,CAAC,sCAAsC,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;YAC9F,OAAO,CAAC,GAAG,CAAC,gCAAgC,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC;YAC1E,OAAO,CAAC,GAAG,CAAC,qBAAqB,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC;YAE7D,sDAAsD;YACtD,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;gBAC9B,IAAI,CAAC;oBACH,MAAM,aAAa,GAAG,IAAI,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBACpD,MAAM,aAAa,CAAC,aAAa,CAAC;wBAChC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa;wBACrC,SAAS,EAAE,MAAM;wBACjB,aAAa,EAAE,KAAK;wBACpB,MAAM,EAAE,IAAI;wBACZ,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY;qBACvC,CAAC,CAAC;oBAEH,wCAAwC;oBACxC,MAAM,aAAa,CAAC,kBAAkB,CAAC;wBACrC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa;wBACrC,SAAS,EAAE,MAAM;wBACjB,aAAa,EAAE,KAAK;wBACpB,MAAM,EAAE,IAAI;wBACZ,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY;qBACvC,CAAC,CAAC;oBAEH,OAAO,CAAC,GAAG,CAAC,kCAAkC,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC;gBAC7E,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAC;gBAClE,CAAC;YACH,CAAC;YAED,0CAA0C;YAC1C,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;gBACvB,OAAO,CAAC,GAAG,CAAC,oCAAoC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;YACxE,CAAC;YACD,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACtC,OAAO,CAAC,GAAG,CAAC,yBAAyB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC5E,CAAC;YACD,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;gBACzB,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;YACtC,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,IAAI;QACR,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;YAC9B,8DAA8D;YAC9D,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;QACnD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACrD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;CACF"}