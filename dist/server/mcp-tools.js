/**
 * MCP tool definitions for the translation server
 */
import { z } from 'zod';
// Import advanced tools
import { setupAnalyzeCodeTool } from '../tools/analyze-code-file.js';
import { setupExtractToTranslationTool } from '../tools/extract-to-translation.js';
import { setupAddTranslationSmartTool } from '../tools/add-translation-smart.js';
import { setupGetTranslationSuggestionsTool } from '../tools/get-translation-suggestions.js';
import { setupSyncTranslationsToFilesTool } from '../tools/sync-translations-to-files.js';
import { setupGenerateTypesTool } from '../tools/generate-types.js';
import { setupInitializeIndexTool } from '../tools/initialize-index.js';
export class MCPTools {
    index;
    fileWatcher;
    config;
    constructor(index, fileWatcher, config) {
        this.index = index;
        this.fileWatcher = fileWatcher;
        this.config = config;
    }
    /**
     * Register all tools with the MCP server using correct SDK format
     */
    registerTools(server) {
        // Search translation tool
        server.tool('search_translation', 'Search for translation keys and values', {
            query: z.string().describe('Search query for keys or values'),
            scope: z.enum(['keys', 'values', 'both']).default('both').describe('Search scope'),
            languages: z.array(z.string()).optional().describe('Specific languages to search in'),
            maxResults: z.number().min(1).max(100).default(20).describe('Maximum number of results'),
            caseSensitive: z.boolean().default(false).describe('Case sensitive search')
        }, async ({ query, scope, languages, maxResults, caseSensitive }) => {
            try {
                const results = await this.index.search(query, {
                    scope,
                    languages,
                    maxResults,
                    caseSensitive
                });
                return {
                    content: [{
                            type: 'text',
                            text: JSON.stringify({
                                query,
                                scope,
                                resultsCount: results.length,
                                results: results.map(result => ({
                                    keyPath: result.keyPath,
                                    matchType: result.matchType,
                                    score: result.score,
                                    translations: result.translations
                                }))
                            }, null, 2)
                        }]
                };
            }
            catch (error) {
                return {
                    content: [{
                            type: 'text',
                            text: `Error searching translations: ${error instanceof Error ? error.message : 'Unknown error'}`
                        }]
                };
            }
        });
        // Get translation context tool
        server.tool('get_translation_context', 'Get translation context with parent, children, and siblings', {
            keyPath: z.string().describe('Translation key path (e.g., "common.buttons.submit")'),
            contextDepth: z.number().min(0).max(5).default(1).describe('Depth of context to retrieve'),
            languages: z.union([z.array(z.string()), z.literal('all')]).default('all').describe('Languages to include')
        }, async ({ keyPath, contextDepth, languages }) => {
            try {
                const context = await this.index.getContext(keyPath, {
                    depth: contextDepth,
                    languages: languages === 'all' ? undefined : languages
                });
                if (!context) {
                    return {
                        content: [{
                                type: 'text',
                                text: `Translation key not found: ${keyPath}`
                            }]
                    };
                }
                return {
                    content: [{
                            type: 'text',
                            text: JSON.stringify({
                                keyPath: context.keyPath,
                                translations: context.translations,
                                parent: context.parent,
                                children: context.children.slice(0, 10), // Limit children for readability
                                siblings: context.siblings.slice(0, 10) // Limit siblings for readability
                            }, null, 2)
                        }]
                };
            }
            catch (error) {
                return {
                    content: [{
                            type: 'text',
                            text: `Error getting translation context: ${error instanceof Error ? error.message : 'Unknown error'}`
                        }]
                };
            }
        });
        // Update translation tool (supports single and bulk operations)
        server.tool('update_translation', 'Update translation values for single or multiple keys', {
            // Single update format (backward compatible)
            keyPath: z.string().optional().describe('Translation key path to update - for single update'),
            updates: z.record(z.string(), z.any()).optional().describe('Language-value pairs to update - for single update'),
            // Bulk update format
            translations: z.array(z.object({
                keyPath: z.string().describe('Translation key path to update'),
                updates: z.record(z.string(), z.any()).describe('Language-value pairs to update')
            })).optional().describe('Array of translations to update - for bulk operations'),
            // Common options
            validateStructure: z.boolean().default(true).describe('Validate structure consistency'),
            writeToFiles: z.boolean().default(true).describe('Write updates to actual files'),
            // Bulk-specific options
            skipOnError: z.boolean().default(true).describe('Skip individual entries on error instead of failing entire batch (bulk only)'),
            batchSize: z.number().min(1).max(100).default(50).describe('Process updates in batches of this size (bulk only)')
        }, async ({ keyPath, updates, translations, validateStructure, writeToFiles, skipOnError, batchSize }) => {
            try {
                // Determine if this is a bulk or single operation
                const isBulkOperation = translations && translations.length > 0;
                if (isBulkOperation) {
                    // Handle bulk operation
                    return await this.handleBulkUpdates(translations, { validateStructure, writeToFiles, skipOnError, batchSize });
                }
                else {
                    // Handle single update (backward compatible)
                    if (!keyPath || !updates) {
                        return {
                            content: [{
                                    type: 'text',
                                    text: JSON.stringify({
                                        error: 'For single update, both keyPath and updates are required',
                                        suggestion: 'Provide keyPath and updates, or use translations array for bulk operations'
                                    }, null, 2)
                                }]
                        };
                    }
                    return await this.handleSingleUpdate({ keyPath, updates }, { validateStructure, writeToFiles });
                }
            }
            catch (error) {
                return {
                    content: [{
                            type: 'text',
                            text: `Error updating translation: ${error instanceof Error ? error.message : 'Unknown error'}`
                        }]
                };
            }
        });
        // Validate structure tool
        server.tool('validate_structure', 'Validate translation structure consistency across languages', {
            baseLanguage: z.string().optional().describe('Base language for validation'),
            fix: z.boolean().default(false).describe('Auto-fix missing translations')
        }, async ({ baseLanguage, fix }) => {
            try {
                const validation = await this.index.validateStructure({
                    baseLanguage: baseLanguage || this.config.baseLanguage,
                    autoFix: fix
                });
                return {
                    content: [{
                            type: 'text',
                            text: JSON.stringify({
                                valid: validation.valid,
                                summary: {
                                    missingKeysCount: Object.values(validation.missingKeys).reduce((sum, keys) => sum + keys.length, 0),
                                    extraKeysCount: Object.values(validation.extraKeys).reduce((sum, keys) => sum + keys.length, 0),
                                    typeMismatchesCount: validation.typeMismatches.length
                                },
                                details: {
                                    missingKeys: validation.missingKeys,
                                    extraKeys: validation.extraKeys,
                                    typeMismatches: validation.typeMismatches,
                                    structuralIssues: validation.structuralIssues
                                }
                            }, null, 2)
                        }]
                };
            }
            catch (error) {
                return {
                    content: [{
                            type: 'text',
                            text: `Error validating structure: ${error instanceof Error ? error.message : 'Unknown error'}`
                        }]
                };
            }
        });
        // Get server statistics tool
        server.tool('get_stats', 'Get server and translation index statistics', {
            includeDetails: z.boolean().default(false).describe('Include detailed statistics'),
            searchKey: z.string().optional().describe('Search for a specific key in the index')
        }, async ({ includeDetails, searchKey }) => {
            try {
                const indexStats = this.index.getStats();
                const watcherStats = this.fileWatcher.getStats();
                const stats = {
                    server: {
                        name: this.config.name,
                        version: this.config.version,
                        baseLanguage: this.config.baseLanguage
                    },
                    index: indexStats,
                    watcher: watcherStats
                };
                if (searchKey) {
                    const keyExists = this.index.has(searchKey);
                    const keyValue = this.index.get(searchKey);
                    return {
                        content: [{
                                type: 'text',
                                text: JSON.stringify({
                                    ...stats,
                                    keySearch: {
                                        searchKey,
                                        exists: keyExists,
                                        value: keyValue,
                                        allKeys: this.index.getKeys().filter(k => k.includes(searchKey.split('.')[0])).slice(0, 20)
                                    }
                                }, null, 2)
                            }]
                    };
                }
                if (includeDetails) {
                    return {
                        content: [{
                                type: 'text',
                                text: JSON.stringify({
                                    ...stats,
                                    details: {
                                        allLanguages: this.index.getLanguages(),
                                        sampleKeys: this.index.getKeys().slice(0, 20),
                                        totalKeys: this.index.getKeys().length,
                                        watchedFiles: this.fileWatcher.getWatchedFiles().slice(0, 10)
                                    }
                                }, null, 2)
                            }]
                    };
                }
                return {
                    content: [{
                            type: 'text',
                            text: JSON.stringify(stats, null, 2)
                        }]
                };
            }
            catch (error) {
                return {
                    content: [{
                            type: 'text',
                            text: `Error getting statistics: ${error instanceof Error ? error.message : 'Unknown error'}`
                        }]
                };
            }
        });
        // Register advanced IDE integration tools
        setupInitializeIndexTool(server, this.index, this.fileWatcher, this.config);
        setupAnalyzeCodeTool(server, this.index);
        setupExtractToTranslationTool(server, this.index, this.config);
        setupAddTranslationSmartTool(server, this.index, this.config);
        setupGetTranslationSuggestionsTool(server, this.index, this.config);
        setupSyncTranslationsToFilesTool(server, this.index, this.fileWatcher, this.config);
        setupGenerateTypesTool(server, this.index, this.config);
    }
    /**
     * Handle single translation update (backward compatible)
     */
    async handleSingleUpdate(update, options) {
        // Validate structure if requested
        if (options.validateStructure) {
            const validation = await this.index.validateStructure();
            if (!validation.valid) {
                return {
                    content: [{
                            type: 'text',
                            text: JSON.stringify({
                                error: 'Structure validation failed before update',
                                issues: validation.structuralIssues
                            }, null, 2)
                        }]
                };
            }
        }
        // Check if key exists
        if (!this.index.has(update.keyPath)) {
            return {
                content: [{
                        type: 'text',
                        text: JSON.stringify({
                            error: 'Translation key not found',
                            keyPath: update.keyPath,
                            suggestion: 'Use add_translation_smart to create new translations'
                        }, null, 2)
                    }]
            };
        }
        // Perform update
        const operations = Object.entries(update.updates).map(([language, value]) => ({
            type: 'set',
            keyPath: update.keyPath,
            language,
            value
        }));
        const result = await this.index.batchUpdate(operations);
        if (!result.success) {
            return {
                content: [{
                        type: 'text',
                        text: JSON.stringify({
                            error: 'Failed to update translation',
                            keyPath: update.keyPath,
                            errors: result.errors
                        }, null, 2)
                    }]
            };
        }
        // Write to files if requested
        const fileWriteResults = {};
        if (options.writeToFiles) {
            for (const [language, value] of Object.entries(update.updates)) {
                try {
                    await this.writeTranslationToFile(language, update.keyPath, value);
                    fileWriteResults[language] = { success: true };
                }
                catch (error) {
                    fileWriteResults[language] = {
                        success: false,
                        error: error instanceof Error ? error.message : 'Unknown error'
                    };
                }
            }
        }
        return {
            content: [{
                    type: 'text',
                    text: JSON.stringify({
                        success: true,
                        keyPath: update.keyPath,
                        updatedLanguages: Object.keys(update.updates),
                        fileWriteResults: options.writeToFiles ? fileWriteResults : undefined
                    }, null, 2)
                }]
        };
    }
    /**
     * Handle bulk translation updates
     */
    async handleBulkUpdates(translations, options) {
        const results = [];
        const errors = [];
        let processed = 0;
        let successful = 0;
        let skipped = 0;
        // Process translations in batches
        for (let i = 0; i < translations.length; i += options.batchSize) {
            const batch = translations.slice(i, i + options.batchSize);
            for (const translation of batch) {
                try {
                    const result = await this.processSingleUpdate(translation, options);
                    results.push(result);
                    processed++;
                    if (result.success) {
                        successful++;
                    }
                    else if (result.skipped) {
                        skipped++;
                    }
                }
                catch (error) {
                    const errorMsg = error instanceof Error ? error.message : 'Unknown error';
                    errors.push(`Update ${processed + 1}: ${errorMsg}`);
                    if (!options.skipOnError) {
                        throw new Error(`Batch failed at update ${processed + 1}: ${errorMsg}`);
                    }
                    results.push({
                        success: false,
                        keyPath: translation.keyPath,
                        updatedLanguages: [],
                        fileWriteResults: {},
                        skipReason: errorMsg
                    });
                    processed++;
                }
            }
        }
        // Calculate summary statistics
        const summary = {
            total: translations.length,
            processed,
            successful,
            skipped,
            failed: processed - successful - skipped,
            errors: errors.length
        };
        return {
            content: [{
                    type: 'text',
                    text: JSON.stringify({
                        success: errors.length === 0 || options.skipOnError,
                        summary,
                        results: results.slice(0, 20), // Limit results for readability
                        errors: errors.slice(0, 10), // Limit errors for readability
                        performance: {
                            batchSize: options.batchSize,
                            totalBatches: Math.ceil(translations.length / options.batchSize)
                        }
                    }, null, 2)
                }]
        };
    }
    /**
     * Process a single translation update
     */
    async processSingleUpdate(translation, options) {
        // Check if key exists
        if (!this.index.has(translation.keyPath)) {
            return {
                success: false,
                keyPath: translation.keyPath,
                updatedLanguages: [],
                fileWriteResults: {},
                skipped: true,
                skipReason: 'Translation key not found'
            };
        }
        // Perform update
        const operations = Object.entries(translation.updates).map(([language, value]) => ({
            type: 'set',
            keyPath: translation.keyPath,
            language,
            value
        }));
        const result = await this.index.batchUpdate(operations);
        if (!result.success) {
            return {
                success: false,
                keyPath: translation.keyPath,
                updatedLanguages: [],
                fileWriteResults: {},
                skipReason: `Failed to update index: ${result.errors?.join(', ')}`
            };
        }
        // Write to files if requested
        const fileWriteResults = {};
        if (options.writeToFiles) {
            for (const [language, value] of Object.entries(translation.updates)) {
                try {
                    await this.writeTranslationToFile(language, translation.keyPath, value);
                    fileWriteResults[language] = { success: true };
                }
                catch (error) {
                    fileWriteResults[language] = {
                        success: false,
                        error: error instanceof Error ? error.message : 'Unknown error'
                    };
                }
            }
        }
        return {
            success: true,
            keyPath: translation.keyPath,
            updatedLanguages: Object.keys(translation.updates),
            fileWriteResults: options.writeToFiles ? fileWriteResults : undefined
        };
    }
    /**
     * Write a single translation to its corresponding file
     */
    async writeTranslationToFile(language, keyPath, value) {
        const { promises: fs } = await import('fs');
        const { join } = await import('path');
        const filePath = join(this.config.translationDir, `${language}.json`);
        // Read existing file content
        let existingData = {};
        try {
            const content = await fs.readFile(filePath, 'utf-8');
            existingData = JSON.parse(content);
        }
        catch (error) {
            // File doesn't exist or is invalid, start with empty object
            existingData = {};
        }
        // Set the nested value
        this.setNestedValue(existingData, keyPath, value);
        // Write back to file with proper formatting
        const newContent = JSON.stringify(existingData, null, 2);
        await fs.writeFile(filePath, newContent, 'utf-8');
    }
    /**
     * Set a nested value in an object using dot notation
     */
    setNestedValue(obj, keyPath, value) {
        const keys = keyPath.split('.');
        let current = obj;
        for (let i = 0; i < keys.length - 1; i++) {
            const key = keys[i];
            if (!key)
                continue; // Skip empty keys
            if (!(key in current) || typeof current[key] !== 'object' || current[key] === null) {
                current[key] = {};
            }
            current = current[key];
        }
        const lastKey = keys[keys.length - 1];
        if (lastKey) {
            current[lastKey] = value;
        }
    }
}
//# sourceMappingURL=mcp-tools.js.map