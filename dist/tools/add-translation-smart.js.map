{"version": 3, "file": "add-translation-smart.js", "sourceRoot": "", "sources": ["../../src/tools/add-translation-smart.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,EAAE,CAAC,EAAE,MAAM,KAAK,CAAC;AACxB,OAAO,EAAE,oBAAoB,EAAE,eAAe,EAAE,MAAM,kCAAkC,CAAC;AAEzF,OAAO,EAAE,QAAQ,IAAI,EAAE,EAAE,MAAM,IAAI,CAAC;AACpC,OAAO,EAAE,IAAI,EAAE,MAAM,MAAM,CAAC;AAE5B;;GAEG;AACH,MAAM,UAAU,4BAA4B,CAAC,MAAW,EAAE,KAAuB,EAAE,MAAW;IAC5F,MAAM,CAAC,IAAI,CACT,uBAAuB,EACvB,8FAA8F,EAC9F;QACE,kDAAkD;QAClD,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,qEAAqE,CAAC;QAC3G,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,wDAAwD,CAAC;QACzH,YAAY,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,6CAA6C,CAAC;QAC3F,OAAO,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,kEAAkE,CAAC;QAE3G,0BAA0B;QAC1B,YAAY,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC;YAC7B,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,4CAA4C,CAAC;YACvE,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,CAAC,+BAA+B,CAAC;YACrF,YAAY,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,oBAAoB,CAAC;YAClE,OAAO,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,yCAAyC,CAAC;SACnF,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,oDAAoD,CAAC;QAE7E,iBAAiB;QACjB,eAAe,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,mCAAmC,CAAC;QACxF,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,kBAAkB,CAAC;QACvG,YAAY,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,yCAAyC,CAAC;QAC3F,SAAS,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,8CAA8C,CAAC;QAC9F,iBAAiB,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,6CAA6C,CAAC;QAEpG,wBAAwB;QACxB,WAAW,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,8EAA8E,CAAC;QAC/H,SAAS,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,0DAA0D,CAAC;KACvH,EACD,KAAK,EAAE,EACL,IAAI,EACJ,SAAS,EACT,YAAY,EACZ,OAAO,EACP,YAAY,EACZ,eAAe,EACf,QAAQ,EACR,YAAY,EACZ,SAAS,EACT,iBAAiB,EACjB,WAAW,EACX,SAAS,EAmBV,EAAE,EAAE;QACH,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAI,oBAAoB,EAAE,CAAC;YAC7C,MAAM,KAAK,GAAG,QAAQ,IAAI,MAAM,CAAC,QAAQ,IAAI,QAAQ,CAAC;YACtD,MAAM,YAAY,GAAG,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC;YAEjD,kDAAkD;YAClD,MAAM,eAAe,GAAG,YAAY,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;YAEhE,IAAI,eAAe,EAAE,CAAC;gBACpB,wBAAwB;gBACxB,OAAO,MAAM,sBAAsB,CACjC,YAAa,EACb,EAAE,eAAe,EAAE,QAAQ,EAAE,KAAK,EAAE,YAAY,EAAE,SAAS,EAAE,iBAAiB,EAAE,WAAW,EAAE,SAAS,EAAE,EACxG,SAAS,EACT,KAAK,EACL,MAAM,EACN,YAAY,CACb,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,kDAAkD;gBAClD,IAAI,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;oBACxB,OAAO;wBACL,OAAO,EAAE,CAAC;gCACR,IAAI,EAAE,MAAM;gCACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;oCACnB,KAAK,EAAE,8DAA8D;oCACrE,UAAU,EAAE,2EAA2E;iCACxF,EAAE,IAAI,EAAE,CAAC,CAAC;6BACZ,CAAC;qBACH,CAAC;gBACJ,CAAC;gBAED,OAAO,MAAM,uBAAuB,CAClC,EAAE,IAAI,EAAE,SAAS,EAAE,YAAY,EAAE,OAAO,EAAE,EAC1C,EAAE,eAAe,EAAE,QAAQ,EAAE,KAAK,EAAE,YAAY,EAAE,SAAS,EAAE,iBAAiB,EAAE,EAChF,SAAS,EACT,KAAK,EACL,MAAM,EACN,YAAY,CACb,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,CAAC;wBACR,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,6BAA6B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE;qBAC9F,CAAC;aACH,CAAC;QACJ,CAAC;IACH,CAAC,CACF,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,uBAAuB,CACpC,WAAyG,EACzG,OAA8H,EAC9H,SAA+B,EAC/B,KAAuB,EACvB,MAAW,EACX,YAAoB;IAEpB,MAAM,MAAM,GAAG,MAAM,wBAAwB,CAC3C,WAAW,EACX,OAAO,EACP,SAAS,EACT,KAAK,EACL,MAAM,EACN,YAAY,CACb,CAAC;IAEF,OAAO;QACL,OAAO,EAAE,CAAC;gBACR,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;aACtC,CAAC;KACH,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,sBAAsB,CACnC,YAAiH,EACjH,OAAuK,EACvK,SAA+B,EAC/B,KAAuB,EACvB,MAAW,EACX,YAAoB;IAEpB,MAAM,OAAO,GAAU,EAAE,CAAC;IAC1B,MAAM,MAAM,GAAa,EAAE,CAAC;IAC5B,IAAI,SAAS,GAAG,CAAC,CAAC;IAClB,IAAI,UAAU,GAAG,CAAC,CAAC;IACnB,IAAI,OAAO,GAAG,CAAC,CAAC;IAEhB,kCAAkC;IAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;QAChE,MAAM,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;QAE3D,KAAK,MAAM,WAAW,IAAI,KAAK,EAAE,CAAC;YAChC,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,wBAAwB,CAC3C,WAAW,EACX,OAAO,EACP,SAAS,EACT,KAAK,EACL,MAAM,EACN,YAAY,CACb,CAAC;gBAEF,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACrB,SAAS,EAAE,CAAC;gBAEZ,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;oBACnB,UAAU,EAAE,CAAC;gBACf,CAAC;qBAAM,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;oBAC1B,OAAO,EAAE,CAAC;gBACZ,CAAC;YAEH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,QAAQ,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;gBAC1E,MAAM,CAAC,IAAI,CAAC,eAAe,SAAS,GAAG,CAAC,KAAK,QAAQ,EAAE,CAAC,CAAC;gBAEzD,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;oBACzB,MAAM,IAAI,KAAK,CAAC,+BAA+B,SAAS,GAAG,CAAC,KAAK,QAAQ,EAAE,CAAC,CAAC;gBAC/E,CAAC;gBAED,OAAO,CAAC,IAAI,CAAC;oBACX,OAAO,EAAE,KAAK;oBACd,GAAG,EAAE,WAAW,CAAC,YAAY,IAAI,SAAS;oBAC1C,YAAY,EAAE,WAAW,CAAC,IAAI;oBAC9B,cAAc,EAAE,EAAE;oBAClB,gBAAgB,EAAE,EAAE;oBACpB,UAAU,EAAE,QAAQ;iBACrB,CAAC,CAAC;gBAEH,SAAS,EAAE,CAAC;YACd,CAAC;QACH,CAAC;IACH,CAAC;IAED,+BAA+B;IAC/B,MAAM,OAAO,GAAG;QACd,KAAK,EAAE,YAAY,CAAC,MAAM;QAC1B,SAAS;QACT,UAAU;QACV,OAAO;QACP,MAAM,EAAE,SAAS,GAAG,UAAU,GAAG,OAAO;QACxC,MAAM,EAAE,MAAM,CAAC,MAAM;KACtB,CAAC;IAEF,OAAO;QACL,OAAO,EAAE,CAAC;gBACR,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;oBACnB,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,OAAO,CAAC,WAAW;oBACnD,OAAO;oBACP,OAAO,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,gCAAgC;oBAC/D,MAAM,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,+BAA+B;oBAC5D,WAAW,EAAE;wBACX,SAAS,EAAE,OAAO,CAAC,SAAS;wBAC5B,YAAY,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC;qBACjE;iBACF,EAAE,IAAI,EAAE,CAAC,CAAC;aACZ,CAAC;KACH,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,wBAAwB,CACrC,WAAyG,EACzG,OAA8H,EAC9H,SAA+B,EAC/B,KAAuB,EACvB,MAAW,EACX,YAAoB;IAEpB,+BAA+B;IAC/B,IAAI,OAAO,GAAG,WAAW,CAAC,YAAY,CAAC;IACvC,IAAI,YAAY,GAAG,KAAK,CAAC;IAEzB,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;QACxC,OAAO,GAAG,SAAS,CAAC,gBAAgB,CAAC,WAAW,CAAC,IAAI,EAAE,WAAW,CAAC,OAAO,EAAE,OAAO,CAAC,QAA0D,CAAC,CAAC;QAChJ,YAAY,GAAG,IAAI,CAAC;IACtB,CAAC;IAED,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,OAAO;YACL,OAAO,EAAE,KAAK;YACd,GAAG,EAAE,SAAS;YACd,YAAY,EAAE,WAAW,CAAC,IAAI;YAC9B,cAAc,EAAE,EAAE;YAClB,gBAAgB,EAAE,EAAE;YACpB,OAAO,EAAE,IAAI;YACb,UAAU,EAAE,iDAAiD;SAC9D,CAAC;IACJ,CAAC;IAED,sBAAsB;IACtB,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;QAClE,OAAO;YACL,OAAO,EAAE,KAAK;YACd,GAAG,EAAE,OAAO;YACZ,YAAY,EAAE,WAAW,CAAC,IAAI;YAC9B,cAAc,EAAE,EAAE;YAClB,gBAAgB,EAAE,EAAE;YACpB,OAAO,EAAE,IAAI;YACb,UAAU,EAAE,iCAAiC,OAAO,CAAC,QAAQ,EAAE;SAChE,CAAC;IACJ,CAAC;IAED,8BAA8B;IAC9B,IAAI,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;QAC7C,OAAO;YACL,OAAO,EAAE,KAAK;YACd,GAAG,EAAE,OAAO;YACZ,YAAY,EAAE,WAAW,CAAC,IAAI;YAC9B,cAAc,EAAE,EAAE;YAClB,gBAAgB,EAAE,EAAE;YACpB,OAAO,EAAE,IAAI;YACb,UAAU,EAAE,gEAAgE;SAC7E,CAAC;IACJ,CAAC;IAED,0CAA0C;IAC1C,IAAI,OAAO,CAAC,YAAY,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;QAC/C,MAAM,mBAAmB,GAAG,MAAM,eAAe,CAAC,uBAAuB,CACvE,WAAW,CAAC,IAAI,EAChB,KAAK,EACL,GAAG,CACJ,CAAC;QAEF,IAAI,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACnC,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,GAAG,EAAE,OAAO;gBACZ,YAAY,EAAE,WAAW,CAAC,IAAI;gBAC9B,cAAc,EAAE,EAAE;gBAClB,gBAAgB,EAAE,EAAE;gBACpB,OAAO,EAAE,IAAI;gBACb,UAAU,EAAE,+BAA+B,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;aAChG,CAAC;QACJ,CAAC;IACH,CAAC;IAED,gDAAgD;IAChD,MAAM,YAAY,GAAG,EAAE,GAAG,WAAW,CAAC,SAAS,EAAE,CAAC;IAClD,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,EAAE,CAAC;QAChC,YAAY,CAAC,YAAY,CAAC,GAAG,WAAW,CAAC,IAAI,CAAC;IAChD,CAAC;IAED,gCAAgC;IAChC,MAAM,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;QACtE,IAAI,EAAE,KAAc;QACpB,OAAO;QACP,QAAQ,EAAE,IAAI;QACd,KAAK;KACN,CAAC,CAAC,CAAC;IAEJ,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;IAEnD,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QACpB,OAAO;YACL,OAAO,EAAE,KAAK;YACd,GAAG,EAAE,OAAO;YACZ,YAAY,EAAE,WAAW,CAAC,IAAI;YAC9B,cAAc,EAAE,EAAE;YAClB,gBAAgB,EAAE,EAAE;YACpB,UAAU,EAAE,2BAA2B,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE;SACnE,CAAC;IACJ,CAAC;IAED,0CAA0C;IAC1C,MAAM,gBAAgB,GAAyD,EAAE,CAAC;IAClF,KAAK,MAAM,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC;QAC7D,IAAI,CAAC;YACH,MAAM,sBAAsB,CAAC,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;YACrF,gBAAgB,CAAC,QAAQ,CAAC,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;QACjD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,gBAAgB,CAAC,QAAQ,CAAC,GAAG;gBAC3B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;aAChE,CAAC;QACJ,CAAC;IACH,CAAC;IAED,OAAO;QACL,OAAO,EAAE,IAAI;QACb,GAAG,EAAE,OAAO;QACZ,YAAY,EAAE,WAAW,CAAC,IAAI;QAC9B,cAAc,EAAE,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC;QACzC,gBAAgB;QAChB,YAAY;QACZ,QAAQ,EAAE,OAAO,CAAC,QAAQ;QAC1B,WAAW,EAAE,OAAO,CAAC,SAAS,IAAI,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC;KACrD,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,sBAAsB,CACnC,QAAgB,EAChB,OAAe,EACf,KAAU,EACV,cAAsB,EACtB,KAAuB;IAEvB,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,EAAE,GAAG,QAAQ,OAAO,CAAC,CAAC;IAE1D,6BAA6B;IAC7B,IAAI,YAAY,GAAQ,EAAE,CAAC;IAC3B,IAAI,CAAC;QACH,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QACrD,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IACrC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,4DAA4D;QAC5D,YAAY,GAAG,EAAE,CAAC;IACpB,CAAC;IAED,uBAAuB;IACvB,cAAc,CAAC,YAAY,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;IAE7C,4CAA4C;IAC5C,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IACzD,MAAM,EAAE,CAAC,SAAS,CAAC,QAAQ,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;AACpD,CAAC;AAED;;GAEG;AACH,SAAS,cAAc,CAAC,GAAQ,EAAE,OAAe,EAAE,KAAU;IAC3D,MAAM,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAChC,IAAI,OAAO,GAAG,GAAG,CAAC;IAElB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;QACzC,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACpB,IAAI,CAAC,GAAG;YAAE,SAAS,CAAC,kBAAkB;QAEtC,IAAI,CAAC,CAAC,GAAG,IAAI,OAAO,CAAC,IAAI,OAAO,OAAO,CAAC,GAAG,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC;YACnF,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;QACpB,CAAC;QACD,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;IACzB,CAAC;IAED,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACtC,IAAI,OAAO,EAAE,CAAC;QACZ,OAAO,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;IAC3B,CAAC;AACH,CAAC"}