/**
 * MCP tool for analyzing code files for translation patterns
 */
import { z } from 'zod';
import { CodeAnalyzer } from '../core/code-analyzer.js';
/**
 * Setup the analyze code file tool
 */
export function setupAnalyzeCodeTool(server, index) {
    server.tool('analyze_code_file', 'Analyze source code file for hardcoded strings and translation usage', {
        filePath: z.string().describe('Path to source code file to analyze'),
        extractHardcoded: z.boolean().default(true).describe('Extract hardcoded text'),
        findUsage: z.boolean().default(true).describe('Find translation key usage'),
        frameworks: z.array(z.enum(['react', 'vue', 'svelte', 'angular'])).optional().describe('Framework-specific analysis'),
        minStringLength: z.number().min(1).max(50).default(3).describe('Minimum string length to consider'),
        excludePatterns: z.array(z.string()).optional().describe('Regex patterns to exclude')
    }, async ({ filePath, extractHardcoded, findUsage, frameworks, minStringLength, excludePatterns }) => {
        try {
            const analyzer = new CodeAnalyzer(frameworks);
            // Convert string patterns to RegExp
            const excludeRegexes = excludePatterns?.map(pattern => {
                try {
                    return new RegExp(pattern);
                }
                catch {
                    return new RegExp(pattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')); // Escape if invalid regex
                }
            }) || [];
            const result = await analyzer.analyzeFile(filePath, {
                extractHardcoded,
                findUsage,
                translationIndex: index,
                minStringLength,
                excludePatterns: excludeRegexes
            });
            // Calculate summary statistics
            const summary = {
                totalHardcodedStrings: result.hardcodedStrings.length,
                highConfidenceStrings: result.hardcodedStrings.filter(s => s.confidence > 0.7).length,
                translationUsageCount: result.translationUsage.length,
                missingTranslations: result.translationUsage.filter(u => !u.exists).length,
                suggestionsCount: result.suggestions.length,
                errorCount: result.suggestions.filter(s => s.severity === 'error').length,
                warningCount: result.suggestions.filter(s => s.severity === 'warning').length
            };
            return {
                content: [{
                        type: 'text',
                        text: JSON.stringify({
                            filePath,
                            detectedFramework: result.detectedFramework,
                            summary,
                            hardcodedStrings: result.hardcodedStrings.map(s => ({
                                text: s.text,
                                line: s.line,
                                column: s.column,
                                confidence: s.confidence,
                                suggestedKey: s.suggestedKey,
                                context: s.context
                            })),
                            translationUsage: result.translationUsage.map(u => ({
                                keyPath: u.keyPath,
                                line: u.line,
                                column: u.column,
                                pattern: u.pattern,
                                exists: u.exists
                            })),
                            suggestions: result.suggestions.map(s => ({
                                type: s.type,
                                message: s.message,
                                line: s.line,
                                action: s.action,
                                severity: s.severity
                            }))
                        }, null, 2)
                    }]
            };
        }
        catch (error) {
            return {
                content: [{
                        type: 'text',
                        text: `Error analyzing code file: ${error instanceof Error ? error.message : 'Unknown error'}`
                    }]
            };
        }
    });
}
//# sourceMappingURL=analyze-code-file.js.map