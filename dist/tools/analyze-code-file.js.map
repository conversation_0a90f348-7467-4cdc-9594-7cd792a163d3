{"version": 3, "file": "analyze-code-file.js", "sourceRoot": "", "sources": ["../../src/tools/analyze-code-file.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,EAAE,CAAC,EAAE,MAAM,KAAK,CAAC;AACxB,OAAO,EAAE,YAAY,EAAE,MAAM,0BAA0B,CAAC;AAIxD;;GAEG;AACH,MAAM,UAAU,oBAAoB,CAAC,MAAW,EAAE,KAAuB;IACvE,MAAM,CAAC,IAAI,CACT,mBAAmB,EACnB,sEAAsE,EACtE;QACE,QAAQ,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,qCAAqC,CAAC;QACpE,gBAAgB,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,wBAAwB,CAAC;QAC9E,SAAS,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,4BAA4B,CAAC;QAC3E,UAAU,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,6BAA6B,CAAC;QACrH,eAAe,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,mCAAmC,CAAC;QACnG,eAAe,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,2BAA2B,CAAC;KACtF,EACD,KAAK,EAAE,EACL,QAAQ,EACR,gBAAgB,EAChB,SAAS,EACT,UAAU,EACV,eAAe,EACf,eAAe,EAQhB,EAAE,EAAE;QACH,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,YAAY,CAAC,UAAU,CAAC,CAAC;YAE9C,oCAAoC;YACpC,MAAM,cAAc,GAAG,eAAe,EAAE,GAAG,CAAC,OAAO,CAAC,EAAE;gBACpD,IAAI,CAAC;oBACH,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC;gBAC7B,CAAC;gBAAC,MAAM,CAAC;oBACP,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,0BAA0B;gBAC/F,CAAC;YACH,CAAC,CAAC,IAAI,EAAE,CAAC;YAET,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,WAAW,CAAC,QAAQ,EAAE;gBAClD,gBAAgB;gBAChB,SAAS;gBACT,gBAAgB,EAAE,KAAK;gBACvB,eAAe;gBACf,eAAe,EAAE,cAAc;aAChC,CAAC,CAAC;YAEH,+BAA+B;YAC/B,MAAM,OAAO,GAAG;gBACd,qBAAqB,EAAE,MAAM,CAAC,gBAAgB,CAAC,MAAM;gBACrD,qBAAqB,EAAE,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC,MAAM;gBACrF,qBAAqB,EAAE,MAAM,CAAC,gBAAgB,CAAC,MAAM;gBACrD,mBAAmB,EAAE,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM;gBAC1E,gBAAgB,EAAE,MAAM,CAAC,WAAW,CAAC,MAAM;gBAC3C,UAAU,EAAE,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,MAAM;gBACzE,YAAY,EAAE,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,SAAS,CAAC,CAAC,MAAM;aAC9E,CAAC;YAEF,OAAO;gBACL,OAAO,EAAE,CAAC;wBACR,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;4BACnB,QAAQ;4BACR,iBAAiB,EAAE,MAAM,CAAC,iBAAiB;4BAC3C,OAAO;4BACP,gBAAgB,EAAE,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gCAClD,IAAI,EAAE,CAAC,CAAC,IAAI;gCACZ,IAAI,EAAE,CAAC,CAAC,IAAI;gCACZ,MAAM,EAAE,CAAC,CAAC,MAAM;gCAChB,UAAU,EAAE,CAAC,CAAC,UAAU;gCACxB,YAAY,EAAE,CAAC,CAAC,YAAY;gCAC5B,OAAO,EAAE,CAAC,CAAC,OAAO;6BACnB,CAAC,CAAC;4BACH,gBAAgB,EAAE,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gCAClD,OAAO,EAAE,CAAC,CAAC,OAAO;gCAClB,IAAI,EAAE,CAAC,CAAC,IAAI;gCACZ,MAAM,EAAE,CAAC,CAAC,MAAM;gCAChB,OAAO,EAAE,CAAC,CAAC,OAAO;gCAClB,MAAM,EAAE,CAAC,CAAC,MAAM;6BACjB,CAAC,CAAC;4BACH,WAAW,EAAE,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gCACxC,IAAI,EAAE,CAAC,CAAC,IAAI;gCACZ,OAAO,EAAE,CAAC,CAAC,OAAO;gCAClB,IAAI,EAAE,CAAC,CAAC,IAAI;gCACZ,MAAM,EAAE,CAAC,CAAC,MAAM;gCAChB,QAAQ,EAAE,CAAC,CAAC,QAAQ;6BACrB,CAAC,CAAC;yBACJ,EAAE,IAAI,EAAE,CAAC,CAAC;qBACZ,CAAC;aACH,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,CAAC;wBACR,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,8BAA8B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE;qBAC/F,CAAC;aACH,CAAC;QACJ,CAAC;IACH,CAAC,CACF,CAAC;AACJ,CAAC"}