/**
 * Translation file integrity checker tool
 * Validates that all translation files have the same structure as the base language
 */
import { TranslationIntegrityResult, ServerConfig } from '../types/translation.js';
/**
 * Setup the check translation integrity tool
 */
export declare function setupCheckTranslationIntegrityTool(server: any, config: Required<ServerConfig>): void;
/**
 * Translation integrity checker implementation
 */
export declare class TranslationIntegrityChecker {
    private readonly config;
    constructor(config: Required<ServerConfig>);
    /**
     * Check integrity of all translation files
     */
    checkIntegrity(options: {
        baseLanguage: string;
        includeDetails: boolean;
        onlyShowIssues: boolean;
        checkTypes: boolean;
    }): Promise<TranslationIntegrityResult>;
    /**
     * Get all translation files in the directory
     */
    private getTranslationFiles;
    /**
     * Load and parse a translation file
     */
    private loadTranslationFile;
    /**
     * Extract language code from filename
     */
    private extractLanguageFromFilename;
    /**
     * Extract all keys from a nested object using dot notation
     */
    private extractAllKeys;
    /**
     * Get value from nested object using path array
     */
    private getValueByPath;
    /**
     * Check a single translation file against the base
     */
    private checkSingleFile;
    /**
     * Generate file-specific recommendations
     */
    private generateFileRecommendations;
    /**
     * Generate global recommendations
     */
    private generateGlobalRecommendations;
    /**
     * Create a summarized version of file result (without detailed arrays)
     */
    private summarizeFileResult;
}
//# sourceMappingURL=check-translation-integrity.d.ts.map