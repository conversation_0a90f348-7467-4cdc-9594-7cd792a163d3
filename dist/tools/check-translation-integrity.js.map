{"version": 3, "file": "check-translation-integrity.js", "sourceRoot": "", "sources": ["../../src/tools/check-translation-integrity.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAEH,OAAO,EAAE,CAAC,EAAE,MAAM,KAAK,CAAC;AACxB,OAAO,EAAE,QAAQ,IAAI,EAAE,EAAE,MAAM,IAAI,CAAC;AACpC,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,MAAM,CAAC;AASrC;;GAEG;AACH,MAAM,UAAU,kCAAkC,CAChD,MAAW,EACX,MAA8B;IAE9B,MAAM,CAAC,IAAI,CACT,6BAA6B,EAC7B,sEAAsE,EACtE;QACE,YAAY,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,uDAAuD,CAAC;QACrG,cAAc,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,yCAAyC,CAAC;QAC7F,cAAc,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,6BAA6B,CAAC;QAClF,UAAU,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,6CAA6C,CAAC;KAC9F,EACD,KAAK,EAAE,EACL,YAAY,EACZ,cAAc,EACd,cAAc,EACd,UAAU,EAMX,EAAE,EAAE;QACH,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,2BAA2B,CAAC,MAAM,CAAC,CAAC;YACxD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,cAAc,CAAC;gBAC1C,YAAY,EAAE,YAAY,IAAI,MAAM,CAAC,YAAY;gBACjD,cAAc;gBACd,cAAc;gBACd,UAAU;aACX,CAAC,CAAC;YAEH,OAAO;gBACL,OAAO,EAAE,CAAC;wBACR,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;qBACtC,CAAC;aACH,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,CAAC;wBACR,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,yCAAyC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE;qBAC1G,CAAC;aACH,CAAC;QACJ,CAAC;IACH,CAAC,CACF,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,OAAO,2BAA2B;IACT;IAA7B,YAA6B,MAA8B;QAA9B,WAAM,GAAN,MAAM,CAAwB;IAAG,CAAC;IAE/D;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,OAKpB;QACC,MAAM,EAAE,YAAY,EAAE,cAAc,EAAE,cAAc,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;QAE7E,4BAA4B;QAC5B,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAE1D,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAClC,MAAM,IAAI,KAAK,CAAC,iCAAiC,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC;QACjF,CAAC;QAED,0BAA0B;QAC1B,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,GAAG,YAAY,OAAO,CAAC,CAAC;QAC9E,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC;QAE9D,IAAI,CAAC,QAAQ,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;YAC1C,MAAM,IAAI,KAAK,CAAC,sBAAsB,YAAY,6BAA6B,CAAC,CAAC;QACnF,CAAC;QAED,kCAAkC;QAClC,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEpD,oBAAoB;QACpB,MAAM,MAAM,GAA+B;YACzC,OAAO,EAAE,IAAI;YACb,YAAY;YACZ,UAAU,EAAE,gBAAgB,CAAC,MAAM;YACnC,OAAO,EAAE;gBACP,SAAS,EAAE,QAAQ,CAAC,MAAM;gBAC1B,eAAe,EAAE,CAAC;gBAClB,gBAAgB,EAAE,CAAC;gBACnB,cAAc,EAAE,CAAC;gBACjB,mBAAmB,EAAE,CAAC;aACvB;YACD,WAAW,EAAE,EAAE;YACf,eAAe,EAAE,EAAE;SACpB,CAAC;QAEF,8BAA8B;QAC9B,KAAK,MAAM,IAAI,IAAI,gBAAgB,EAAE,CAAC;YACpC,MAAM,QAAQ,GAAG,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,CAAC;YAExD,qBAAqB;YACrB,IAAI,QAAQ,KAAK,YAAY,EAAE,CAAC;gBAC9B,SAAS;YACX,CAAC;YAED,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,eAAe,CAC3C,IAAI,EACJ,QAAQ,EACR,QAAQ,CAAC,IAAI,EACb,QAAQ,EACR,UAAU,CACX,CAAC;YAEF,iBAAiB;YACjB,IAAI,UAAU,CAAC,KAAK,CAAC,WAAW,GAAG,CAAC;gBAChC,UAAU,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC;gBAC9B,UAAU,CAAC,KAAK,CAAC,cAAc,GAAG,CAAC;gBACnC,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;gBAC1B,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;gBACvB,MAAM,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;YACnC,CAAC;YAED,MAAM,CAAC,OAAO,CAAC,gBAAgB,IAAI,UAAU,CAAC,KAAK,CAAC,WAAW,CAAC;YAChE,MAAM,CAAC,OAAO,CAAC,cAAc,IAAI,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC;YAC5D,MAAM,CAAC,OAAO,CAAC,mBAAmB,IAAI,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC;YAEtE,mDAAmD;YACnD,IAAI,CAAC,cAAc,IAAI,UAAU,CAAC,KAAK,CAAC,WAAW,GAAG,CAAC;gBACnD,UAAU,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,IAAI,UAAU,CAAC,KAAK,CAAC,cAAc,GAAG,CAAC;gBACrE,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;gBAC1B,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;YACpG,CAAC;QACH,CAAC;QAED,kCAAkC;QAClC,MAAM,CAAC,eAAe,GAAG,IAAI,CAAC,6BAA6B,CAAC,MAAM,CAAC,CAAC;QAEpE,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB;QAC/B,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;YAC3D,OAAO,KAAK;iBACT,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,OAAO,CAAC;iBACzC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC,CAAC;QACzD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,sCAAsC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;QACpH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB,CAAC,QAAgB;QAKhD,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YACrD,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACjC,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;QACnC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,SAAS,EAAE,KAAK;gBAChB,UAAU,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,qBAAqB;aAC3E,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,2BAA2B,CAAC,QAAgB;QAClD,MAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC;QACjD,OAAO,QAAQ,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,GAAQ,EAAE,SAAiB,EAAE;QAClD,MAAM,IAAI,GAA6E,EAAE,CAAC;QAE1F,IAAI,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;YAC1D,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC/C,MAAM,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;gBAClD,MAAM,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;gBAEhE,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;oBAChE,kCAAkC;oBAClC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC;gBACpD,CAAC;qBAAM,CAAC;oBACN,YAAY;oBACZ,IAAI,CAAC,IAAI,CAAC;wBACR,OAAO;wBACP,KAAK;wBACL,IAAI,EAAE,OAAO,KAAK;wBAClB,QAAQ;qBACT,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,GAAQ,EAAE,IAAc;QAC7C,IAAI,OAAO,GAAG,GAAG,CAAC;QAClB,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,IAAI,OAAO,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,GAAG,IAAI,OAAO,EAAE,CAAC;gBAC7D,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;YACzB,CAAC;iBAAM,CAAC;gBACN,OAAO,SAAS,CAAC;YACnB,CAAC;QACH,CAAC;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe,CAC3B,QAAgB,EAChB,QAAgB,EAChB,QAAa,EACb,QAAkF,EAClF,UAAmB;QAEnB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;QAE1D,MAAM,MAAM,GAAwB;YAClC,QAAQ;YACR,QAAQ;YACR,MAAM,EAAE,IAAI;YACZ,SAAS,EAAE,QAAQ,CAAC,SAAS;YAC7B,UAAU,EAAE,QAAQ,CAAC,UAAU;YAC/B,KAAK,EAAE;gBACL,SAAS,EAAE,CAAC;gBACZ,WAAW,EAAE,CAAC;gBACd,SAAS,EAAE,CAAC;gBACZ,cAAc,EAAE,CAAC;gBACjB,YAAY,EAAE,CAAC;aAChB;YACD,WAAW,EAAE,EAAE;YACf,SAAS,EAAE,EAAE;YACb,cAAc,EAAE,EAAE;YAClB,eAAe,EAAE,EAAE;SACpB,CAAC;QAEF,IAAI,CAAC,QAAQ,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;YAC1C,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,6BAA6B,QAAQ,OAAO,CAAC,CAAC;YAC1E,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,8BAA8B;QAC9B,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACpD,MAAM,UAAU,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAE9D,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC;QAEzC,yBAAyB;QACzB,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;gBACrC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC;oBACtB,OAAO,EAAE,OAAO,CAAC,OAAO;oBACxB,aAAa,EAAE,OAAO,CAAC,KAAK;oBAC5B,YAAY,EAAE,OAAO,CAAC,IAAI;oBAC1B,QAAQ,EAAE,OAAO,CAAC,QAAQ;iBAC3B,CAAC,CAAC;gBACH,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;YAC7B,CAAC;iBAAM,IAAI,UAAU,EAAE,CAAC;gBACtB,4BAA4B;gBAC5B,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAE,CAAC;gBACjD,IAAI,OAAO,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI,EAAE,CAAC;oBAClC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC;wBACzB,OAAO,EAAE,OAAO,CAAC,OAAO;wBACxB,YAAY,EAAE,OAAO,CAAC,IAAI;wBAC1B,UAAU,EAAE,OAAO,CAAC,IAAI;wBACxB,aAAa,EAAE,OAAO,CAAC,KAAK;wBAC5B,WAAW,EAAE,OAAO,CAAC,KAAK;wBAC1B,QAAQ,EAAE,OAAO,CAAC,QAAQ;qBAC3B,CAAC,CAAC;oBACH,MAAM,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC;gBAChC,CAAC;YACH,CAAC;QACH,CAAC;QAED,uBAAuB;QACvB,MAAM,UAAU,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;QACzD,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;gBACrC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC;oBACpB,OAAO,EAAE,OAAO,CAAC,OAAO;oBACxB,WAAW,EAAE,OAAO,CAAC,KAAK;oBAC1B,UAAU,EAAE,OAAO,CAAC,IAAI;oBACxB,QAAQ,EAAE,OAAO,CAAC,QAAQ;iBAC3B,CAAC,CAAC;gBACH,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;YAC3B,CAAC;QACH,CAAC;QAED,yBAAyB;QACzB,MAAM,cAAc,GAAG,QAAQ,CAAC,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC;QAClE,MAAM,CAAC,KAAK,CAAC,YAAY,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAEvF,yCAAyC;QACzC,MAAM,CAAC,eAAe,GAAG,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,CAAC;QAElE,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,2BAA2B,CAAC,UAA+B;QACjE,MAAM,eAAe,GAAa,EAAE,CAAC;QAErC,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;YAC1B,eAAe,CAAC,IAAI,CAAC,6BAA6B,UAAU,CAAC,QAAQ,OAAO,CAAC,CAAC;YAC9E,OAAO,eAAe,CAAC;QACzB,CAAC;QAED,IAAI,UAAU,CAAC,KAAK,CAAC,WAAW,GAAG,CAAC,EAAE,CAAC;YACrC,eAAe,CAAC,IAAI,CAClB,OAAO,UAAU,CAAC,KAAK,CAAC,WAAW,uBAAuB,UAAU,CAAC,KAAK,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,OAAO,UAAU,CAAC,QAAQ,OAAO,CACvI,CAAC;YAEF,sCAAsC;YACtC,MAAM,UAAU,GAAG,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACtD,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC1B,eAAe,CAAC,IAAI,CAClB,yBAAyB,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CACtH,CAAC;YACJ,CAAC;QACH,CAAC;QAED,IAAI,UAAU,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC;YACnC,eAAe,CAAC,IAAI,CAClB,UAAU,UAAU,CAAC,KAAK,CAAC,SAAS,aAAa,UAAU,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,SAAS,UAAU,CAAC,QAAQ,OAAO,CAC9H,CAAC;YAEF,oCAAoC;YACpC,MAAM,QAAQ,GAAG,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAClD,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACxB,eAAe,CAAC,IAAI,CAClB,uBAAuB,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAChH,CAAC;YACJ,CAAC;QACH,CAAC;QAED,IAAI,UAAU,CAAC,KAAK,CAAC,cAAc,GAAG,CAAC,EAAE,CAAC;YACxC,eAAe,CAAC,IAAI,CAClB,OAAO,UAAU,CAAC,KAAK,CAAC,cAAc,iBAAiB,UAAU,CAAC,KAAK,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,UAAU,CAAC,QAAQ,OAAO,CACxI,CAAC;YAEF,yCAAyC;YACzC,MAAM,aAAa,GAAG,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC5D,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC7B,eAAe,CAAC,IAAI,CAClB,oBAAoB,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,OAAO,cAAc,CAAC,CAAC,YAAY,SAAS,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAC1H,CAAC;YACJ,CAAC;QACH,CAAC;QAED,IAAI,UAAU,CAAC,KAAK,CAAC,YAAY,GAAG,CAAC,EAAE,CAAC;YACtC,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,YAAY,GAAG,GAAG,CAAC,CAAC;YACnE,eAAe,CAAC,IAAI,CAAC,6BAA6B,UAAU,MAAM,UAAU,CAAC,KAAK,CAAC,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC,WAAW,IAAI,UAAU,CAAC,KAAK,CAAC,SAAS,QAAQ,CAAC,CAAC;QACrK,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAED;;OAEG;IACK,6BAA6B,CAAC,MAAkC;QACtE,MAAM,eAAe,GAAa,EAAE,CAAC;QAErC,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,eAAe,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;YAC1E,OAAO,eAAe,CAAC;QACzB,CAAC;QAED,IAAI,MAAM,CAAC,OAAO,CAAC,eAAe,GAAG,CAAC,EAAE,CAAC;YACvC,eAAe,CAAC,IAAI,CAClB,MAAM,MAAM,CAAC,OAAO,CAAC,eAAe,QAAQ,MAAM,CAAC,OAAO,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,QAAQ,MAAM,CAAC,OAAO,CAAC,eAAe,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,YAAY,CAC7J,CAAC;QACJ,CAAC;QAED,IAAI,MAAM,CAAC,OAAO,CAAC,gBAAgB,GAAG,CAAC,EAAE,CAAC;YACxC,eAAe,CAAC,IAAI,CAClB,UAAU,MAAM,CAAC,OAAO,CAAC,gBAAgB,uBAAuB,MAAM,CAAC,OAAO,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,mBAAmB,CAClI,CAAC;QACJ,CAAC;QAED,IAAI,MAAM,CAAC,OAAO,CAAC,cAAc,GAAG,CAAC,EAAE,CAAC;YACtC,eAAe,CAAC,IAAI,CAClB,cAAc,MAAM,CAAC,OAAO,CAAC,cAAc,aAAa,MAAM,CAAC,OAAO,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,oCAAoC,CACzI,CAAC;QACJ,CAAC;QAED,IAAI,MAAM,CAAC,OAAO,CAAC,mBAAmB,GAAG,CAAC,EAAE,CAAC;YAC3C,eAAe,CAAC,IAAI,CAClB,UAAU,MAAM,CAAC,OAAO,CAAC,mBAAmB,iBAAiB,MAAM,CAAC,OAAO,CAAC,mBAAmB,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,+BAA+B,CAC/I,CAAC;QACJ,CAAC;QAED,2BAA2B;QAC3B,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CACjE,CAAC,CAAC,CAAC,SAAS,IAAI,CAAC,CAAC,KAAK,CAAC,YAAY,GAAG,GAAG,CAC3C,CAAC;QAEF,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7B,eAAe,CAAC,IAAI,CAClB,oBAAoB,aAAa,CAAC,MAAM,QAAQ,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,uDAAuD,CAC3I,CAAC;QACJ,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,UAA+B;QACzD,OAAO;YACL,GAAG,UAAU;YACb,WAAW,EAAE,EAAE,EAAE,0CAA0C;YAC3D,SAAS,EAAE,EAAE;YACb,cAAc,EAAE,EAAE;YAClB,eAAe,EAAE,UAAU,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,kCAAkC;SAC3F,CAAC;IACJ,CAAC;CACF"}