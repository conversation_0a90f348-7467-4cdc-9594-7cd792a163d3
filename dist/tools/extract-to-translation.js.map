{"version": 3, "file": "extract-to-translation.js", "sourceRoot": "", "sources": ["../../src/tools/extract-to-translation.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,EAAE,CAAC,EAAE,MAAM,KAAK,CAAC;AACxB,OAAO,EAAE,oBAAoB,EAAE,eAAe,EAAE,MAAM,kCAAkC,CAAC;AAIzF;;GAEG;AACH,MAAM,UAAU,6BAA6B,CAAC,MAAW,EAAE,KAAuB,EAAE,MAAW;IAC7F,MAAM,CAAC,IAAI,CACT,wBAAwB,EACxB,2EAA2E,EAC3E;QACE,QAAQ,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,uCAAuC,CAAC;QACtE,aAAa,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,+BAA+B,CAAC;QACnE,SAAS,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,yDAAyD,CAAC;QACpG,aAAa,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,mDAAmD,CAAC;QACvG,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,mCAAmC,CAAC;QACjH,YAAY,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,mCAAmC,CAAC;QACjF,mBAAmB,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,kCAAkC,CAAC;QAC7G,YAAY,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,yCAAyC,CAAC;QAC3F,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,kBAAkB,CAAC;KACxG,EACD,KAAK,EAAE,EACL,QAAQ,EACR,aAAa,EACb,SAAS,EACT,aAAa,EACb,SAAS,EACT,YAAY,EACZ,mBAAmB,EACnB,YAAY,EACZ,QAAQ,EAWT,EAAE,EAAE;QACH,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAI,oBAAoB,CAAC,SAAS,CAAC,CAAC;YACtD,MAAM,QAAQ,GAAG,YAAY,IAAI,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC;YAC7D,MAAM,KAAK,GAAG,QAAQ,IAAI,MAAM,CAAC,QAAQ,IAAI,QAAQ,CAAC;YAEtD,+BAA+B;YAC/B,IAAI,GAAG,GAAG,SAAS,CAAC;YACpB,IAAI,CAAC,GAAG,EAAE,CAAC;gBACT,GAAG,GAAG,SAAS,CAAC,gBAAgB,CAAC,aAAa,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;YACnE,CAAC;YAED,sBAAsB;YACtB,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC;gBACnD,OAAO;oBACL,OAAO,EAAE,CAAC;4BACR,IAAI,EAAE,MAAM;4BACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;gCACnB,KAAK,EAAE,oBAAoB;gCAC3B,GAAG;gCACH,cAAc,EAAE,KAAK;gCACrB,UAAU,EAAE,SAAS,CAAC,gBAAgB,CAAC,aAAa,EAAE,QAAQ,EAAE,KAAK,CAAC;6BACvE,EAAE,IAAI,EAAE,CAAC,CAAC;yBACZ,CAAC;iBACH,CAAC;YACJ,CAAC;YAED,0CAA0C;YAC1C,IAAI,mBAAmB,GAAU,EAAE,CAAC;YACpC,IAAI,YAAY,EAAE,CAAC;gBACjB,mBAAmB,GAAG,MAAM,eAAe,CAAC,uBAAuB,CACjE,aAAa,EACb,KAAK,EACL,GAAG,CACJ,CAAC;gBAEF,IAAI,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACnC,OAAO;wBACL,OAAO,EAAE,CAAC;gCACR,IAAI,EAAE,MAAM;gCACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;oCACnB,OAAO,EAAE,4BAA4B;oCACrC,YAAY,EAAE,GAAG;oCACjB,aAAa;oCACb,mBAAmB;oCACnB,OAAO,EAAE,KAAK;oCACd,cAAc,EAAE,+CAA+C;iCAChE,EAAE,IAAI,EAAE,CAAC,CAAC;6BACZ,CAAC;qBACH,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,8BAA8B;YAC9B,IAAI,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;gBACnB,MAAM,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;gBAC1C,OAAO;oBACL,OAAO,EAAE,CAAC;4BACR,IAAI,EAAE,MAAM;4BACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;gCACnB,KAAK,EAAE,gCAAgC;gCACvC,GAAG;gCACH,aAAa,EAAE,QAAQ,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,OAAO,IAAI,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ;gCAC1G,UAAU,EAAE,GAAG,GAAG,MAAM,CAAC,sBAAsB;6BAChD,EAAE,IAAI,EAAE,CAAC,CAAC;yBACZ,CAAC;iBACH,CAAC;YACJ,CAAC;YAED,sCAAsC;YACtC,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;YAE9C,uCAAuC;YACvC,MAAM,cAAc,GAAG,CAAC,QAAQ,CAAC,CAAC;YAClC,IAAI,mBAAmB,EAAE,CAAC;gBACxB,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,EAAE,CAAC;oBAChE,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;oBAClC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC5B,CAAC;YACH,CAAC;YAED,sCAAsC;YACtC,IAAI,cAAc,GAAkB,IAAI,CAAC;YACzC,IAAI,kBAAkB,GAAkB,IAAI,CAAC;YAE7C,IAAI,aAAa,EAAE,CAAC;gBAClB,IAAI,CAAC;oBACH,cAAc,GAAG,MAAM,SAAS,CAAC,0BAA0B,CACzD,QAAQ,EACR,aAAa,EACb,GAAG,CACJ,CAAC;oBACF,kBAAkB,GAAG,SAAS,CAAC,qBAAqB,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;gBACvE,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO;wBACL,OAAO,EAAE,CAAC;gCACR,IAAI,EAAE,MAAM;gCACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;oCACnB,cAAc,EAAE,IAAI;oCACpB,GAAG;oCACH,gBAAgB,EAAE,IAAI;oCACtB,gBAAgB,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;oCAC1E,wBAAwB,EAAE,SAAS,CAAC,qBAAqB,CAAC,GAAG,EAAE,SAAS,CAAC;iCAC1E,EAAE,IAAI,EAAE,CAAC,CAAC;6BACZ,CAAC;qBACH,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,CAAC;wBACR,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;4BACnB,OAAO,EAAE,IAAI;4BACb,GAAG;4BACH,YAAY,EAAE,aAAa;4BAC3B,QAAQ;4BACR,cAAc;4BACd,QAAQ,EAAE,aAAa;4BACvB,kBAAkB,EAAE,kBAAkB,IAAI,SAAS,CAAC,qBAAqB,CAAC,GAAG,EAAE,SAAS,CAAC;4BACzF,iBAAiB,EAAE,SAAS;4BAC5B,QAAQ,EAAE,KAAK;4BACf,cAAc,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,2BAA2B,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI;yBAC7F,EAAE,IAAI,EAAE,CAAC,CAAC;qBACZ,CAAC;aACH,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,CAAC;wBACR,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,iCAAiC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE;qBAClG,CAAC;aACH,CAAC;QACJ,CAAC;IACH,CAAC,CACF,CAAC;AACJ,CAAC"}