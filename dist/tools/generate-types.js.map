{"version": 3, "file": "generate-types.js", "sourceRoot": "", "sources": ["../../src/tools/generate-types.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,EAAE,CAAC,EAAE,MAAM,KAAK,CAAC;AACxB,OAAO,EAAE,aAAa,EAAE,MAAM,2BAA2B,CAAC;AAG1D;;GAEG;AACH,MAAM,UAAU,sBAAsB,CAAC,MAAW,EAAE,KAAuB,EAAE,MAAW;IACtF,MAAM,CAAC,IAAI,CACT,gBAAgB,EAChB,gDAAgD,EAChD;QACE,UAAU,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,gDAAgD,CAAC;QAC5F,SAAS,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,oCAAoC,CAAC;QACpF,aAAa,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,kDAAkD,CAAC;QACtG,MAAM,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,qCAAqC,CAAC;QACjF,YAAY,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,kCAAkC,CAAC;QAChF,KAAK,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,gDAAgD,CAAC;QAC5F,QAAQ,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,0BAA0B,CAAC;KACzE,EACD,KAAK,EAAE,EACL,UAAU,EACV,SAAS,EACT,aAAa,EACb,MAAM,EACN,YAAY,EACZ,KAAK,EACL,QAAQ,EAST,EAAE,EAAE;QACH,IAAI,CAAC;YACH,MAAM,aAAa,GAAG,IAAI,aAAa,CAAC,KAAK,CAAC,CAAC;YAC/C,MAAM,eAAe,GAAG,UAAU,IAAI,MAAM,CAAC,aAAa,CAAC;YAE3D,IAAI,CAAC,eAAe,EAAE,CAAC;gBACrB,OAAO;oBACL,OAAO,EAAE,CAAC;4BACR,IAAI,EAAE,MAAM;4BACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;gCACnB,KAAK,EAAE,0BAA0B;gCACjC,UAAU,EAAE,6DAA6D;6BAC1E,EAAE,IAAI,EAAE,CAAC,CAAC;yBACZ,CAAC;iBACH,CAAC;YACJ,CAAC;YAED,MAAM,iBAAiB,GAAG,YAAY,IAAI,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC;YAEtE,iBAAiB;YACjB,MAAM,aAAa,CAAC,aAAa,CAAC;gBAChC,UAAU,EAAE,eAAe;gBAC3B,SAAS;gBACT,aAAa;gBACb,MAAM;gBACN,YAAY,EAAE,iBAAiB;aAChC,CAAC,CAAC;YAEH,wBAAwB;YACxB,IAAI,gBAAgB,GAAG,IAAI,CAAC;YAC5B,IAAI,QAAQ,EAAE,CAAC;gBACb,MAAM,EAAE,mBAAmB,EAAE,GAAG,MAAM,MAAM,CAAC,2BAA2B,CAAC,CAAC;gBAC1E,gBAAgB,GAAG,MAAM,mBAAmB,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC;YAC9E,CAAC;YAED,+BAA+B;YAC/B,IAAI,KAAK,EAAE,CAAC;gBACV,MAAM,aAAa,CAAC,kBAAkB,CAAC;oBACrC,UAAU,EAAE,eAAe;oBAC3B,SAAS;oBACT,aAAa;oBACb,MAAM;oBACN,YAAY,EAAE,iBAAiB;iBAChC,CAAC,CAAC;YACL,CAAC;YAED,iBAAiB;YACjB,MAAM,KAAK,GAAG;gBACZ,SAAS,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,MAAM;gBACjC,SAAS,EAAE,KAAK,CAAC,YAAY,EAAE;gBAC/B,UAAU,EAAE,eAAe;gBAC3B,QAAQ,EAAE,MAAM,WAAW,CAAC,eAAe,CAAC;aAC7C,CAAC;YAEF,OAAO;gBACL,OAAO,EAAE,CAAC;wBACR,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;4BACnB,OAAO,EAAE,IAAI;4BACb,UAAU,EAAE,eAAe;4BAC3B,SAAS;4BACT,OAAO,EAAE;gCACP,aAAa;gCACb,MAAM;gCACN,YAAY,EAAE,iBAAiB;gCAC/B,KAAK;6BACN;4BACD,KAAK;4BACL,UAAU,EAAE,gBAAgB;4BAC5B,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC,0CAA0C,CAAC,CAAC,CAAC,8BAA8B;yBAC7F,EAAE,IAAI,EAAE,CAAC,CAAC;qBACZ,CAAC;aACH,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,CAAC;wBACR,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,2BAA2B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE;qBAC5F,CAAC;aACH,CAAC;QACJ,CAAC;IACH,CAAC,CACF,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,WAAW,CAAC,QAAgB;IACzC,IAAI,CAAC;QACH,MAAM,EAAE,QAAQ,EAAE,EAAE,EAAE,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,CAAC;QAC5C,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACtC,OAAO,KAAK,CAAC,IAAI,CAAC;IACpB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,CAAC;IACX,CAAC;AACH,CAAC"}