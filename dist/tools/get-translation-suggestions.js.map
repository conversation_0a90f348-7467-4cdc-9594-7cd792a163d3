{"version": 3, "file": "get-translation-suggestions.js", "sourceRoot": "", "sources": ["../../src/tools/get-translation-suggestions.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,EAAE,CAAC,EAAE,MAAM,KAAK,CAAC;AAGxB;;GAEG;AACH,MAAM,UAAU,kCAAkC,CAAC,MAAW,EAAE,KAAuB,EAAE,MAAW;IAClG,MAAM,CAAC,IAAI,CACT,6BAA6B,EAC7B,gEAAgE,EAChE;QACE,OAAO,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,wCAAwC,CAAC;QACtE,cAAc,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,+BAA+B,CAAC;QAChG,aAAa,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,uCAAuC,CAAC;QAC1F,iBAAiB,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,iDAAiD,CAAC;QACpG,eAAe,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,mDAAmD,CAAC;QACzG,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,cAAc,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,4BAA4B,CAAC;QAClH,QAAQ,EAAE,CAAC,CAAC,MAAM,CAAC;YACjB,QAAQ,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,8BAA8B,CAAC;YACzE,QAAQ,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,6CAA6C,CAAC;YACvF,OAAO,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,6BAA6B,CAAC;SACvE,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,oBAAoB,CAAC;KAC7C,EACD,KAAK,EAAE,EACL,OAAO,EACP,cAAc,EACd,aAAa,EACb,iBAAiB,EACjB,eAAe,EACf,MAAM,EACN,QAAQ,EAaT,EAAE,EAAE;QACH,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,iBAAiB,IAAI,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC;YAEtE,+BAA+B;YAC/B,IAAI,WAAW,GAAG,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YAEhD,gBAAgB;YAChB,IAAI,QAAQ,EAAE,CAAC;gBACb,IAAI,QAAQ,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;oBACpC,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE;wBACzC,MAAM,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;wBAC/C,MAAM,QAAQ,GAAG,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,IAAI,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC;wBACvF,OAAO,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;oBAClD,CAAC,CAAC,CAAC;gBACL,CAAC;gBAED,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;oBACtB,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE;wBACzC,OAAO,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC,QAAS,CAAC,KAAK,IAAI,CAAC;oBACzD,CAAC,CAAC,CAAC;gBACL,CAAC;gBAED,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;oBACrB,IAAI,CAAC;wBACH,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;wBAC3C,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;oBACnE,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,qCAAqC;oBACvC,CAAC;gBACH,CAAC;YACH,CAAC;YAED,mBAAmB;YACnB,QAAQ,MAAM,EAAE,CAAC;gBACf,KAAK,cAAc;oBACjB,WAAW,CAAC,IAAI,EAAE,CAAC;oBACnB,MAAM;gBACR,KAAK,OAAO;oBACV,oEAAoE;oBACpE,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;wBACxB,MAAM,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;wBACnC,MAAM,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;wBACnC,OAAO,MAAM,GAAG,MAAM,CAAC;oBACzB,CAAC,CAAC,CAAC;oBACH,MAAM;gBACR,KAAK,WAAW,CAAC;gBACjB;oBACE,+DAA+D;oBAC/D,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;wBACxB,MAAM,kBAAkB,GAAG,CAAC,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC;wBAC7E,MAAM,kBAAkB,GAAG,CAAC,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC;wBAE7E,IAAI,kBAAkB,IAAI,CAAC,kBAAkB;4BAAE,OAAO,CAAC,CAAC,CAAC;wBACzD,IAAI,CAAC,kBAAkB,IAAI,kBAAkB;4BAAE,OAAO,CAAC,CAAC;wBAExD,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC;oBAC7B,CAAC,CAAC,CAAC;oBACH,MAAM;YACV,CAAC;YAED,gBAAgB;YAChB,MAAM,kBAAkB,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC;YAEhE,uBAAuB;YACvB,MAAM,OAAO,GAAG,kBAAkB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;gBAC/C,MAAM,UAAU,GAAQ;oBACtB,OAAO;oBACP,KAAK,EAAE,uBAAuB,CAAC,OAAO,EAAE,OAAO,CAAC;iBACjD,CAAC;gBAEF,IAAI,aAAa,EAAE,CAAC;oBAClB,MAAM,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;oBAC/C,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,IAAI,KAAK,EAAE,CAAC;wBAC3D,UAAU,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;wBAC/B,UAAU,CAAC,QAAQ,GAAG,YAAY,CAAC;oBACrC,CAAC;oBAED,uDAAuD;oBACvD,IAAI,eAAe,EAAE,CAAC;wBACpB,MAAM,YAAY,GAAG,KAAK,CAAC,YAAY,EAAE,CAAC;wBAC1C,UAAU,CAAC,YAAY,GAAG,EAAE,CAAC;wBAC7B,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;4BAC1B,MAAM,SAAS,GAAG,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;4BAC3C,IAAI,SAAS,IAAI,OAAO,SAAS,KAAK,QAAQ,IAAI,OAAO,IAAI,SAAS,EAAE,CAAC;gCACvE,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC;4BAClD,CAAC;wBACH,CAAC,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;gBAED,IAAI,eAAe,EAAE,CAAC;oBACpB,MAAM,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;oBAC/C,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;wBACvC,UAAU,CAAC,QAAQ,GAAG;4BACpB,IAAI,EAAE,KAAK,CAAC,IAAI;4BAChB,IAAI,EAAE,KAAK,CAAC,IAAI;4BAChB,MAAM,EAAE,KAAK,CAAC,MAAM;4BACpB,YAAY,EAAE,KAAK,CAAC,YAAY;yBACjC,CAAC;oBACJ,CAAC;oBAED,6BAA6B;oBAC7B,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;oBACjC,UAAU,CAAC,SAAS,GAAG;wBACrB,KAAK,EAAE,KAAK,CAAC,MAAM;wBACnB,MAAM,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI;wBAC9D,GAAG,EAAE,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;wBAC5B,SAAS,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI;qBAC9C,CAAC;gBACJ,CAAC;gBAED,OAAO,UAAU,CAAC;YACpB,CAAC,CAAC,CAAC;YAEH,yBAAyB;YACzB,MAAM,OAAO,GAAG;gBACd,YAAY,EAAE,WAAW,CAAC,MAAM;gBAChC,OAAO,EAAE,kBAAkB,CAAC,MAAM;gBAClC,OAAO,EAAE,WAAW,CAAC,MAAM,GAAG,cAAc;gBAC5C,WAAW,EAAE,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM;gBACtC,kBAAkB,EAAE,KAAK,CAAC,YAAY,EAAE;gBACxC,YAAY;aACb,CAAC;YAEF,yCAAyC;YACzC,MAAM,kBAAkB,GAAG,qBAAqB,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;YAEpE,OAAO;gBACL,OAAO,EAAE,CAAC;wBACR,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;4BACnB,OAAO;4BACP,OAAO;4BACP,WAAW,EAAE,OAAO;4BACpB,OAAO,EAAE,kBAAkB;4BAC3B,OAAO,EAAE,QAAQ;4BACjB,QAAQ,EAAE,MAAM;yBACjB,EAAE,IAAI,EAAE,CAAC,CAAC;qBACZ,CAAC;aACH,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,CAAC;wBACR,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,0CAA0C,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE;qBAC3G,CAAC;aACH,CAAC;QACJ,CAAC;IACH,CAAC,CACF,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAS,uBAAuB,CAAC,OAAe,EAAE,OAAe;IAC/D,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;IACvC,MAAM,YAAY,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;IAE3C,IAAI,KAAK,GAAG,CAAC,CAAC;IAEd,wCAAwC;IACxC,IAAI,QAAQ,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;QACtC,KAAK,IAAI,GAAG,CAAC;IACf,CAAC;IAED,yCAAyC;SACpC,IAAI,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;QACzC,KAAK,IAAI,GAAG,CAAC;IACf,CAAC;IAED,0CAA0C;IAC1C,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;IAC7D,KAAK,IAAI,UAAU,GAAG,IAAI,CAAC;IAE3B,qCAAqC;IACrC,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;IACxC,KAAK,IAAI,KAAK,GAAG,GAAG,CAAC;IAErB,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;AACzC,CAAC;AAED;;GAEG;AACH,SAAS,qBAAqB,CAAC,OAAe,EAAE,KAAuB,EAAE,UAAkB;IACzF,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACjC,MAAM,OAAO,GAAU,EAAE,CAAC;IAE1B,6BAA6B;IAC7B,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACrB,MAAM,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAChD,MAAM,iBAAiB,GAAG,KAAK,CAAC,cAAc,CAAC,UAAU,CAAC;aACvD,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,OAAO,IAAI,GAAG,CAAC,UAAU,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC;aAClE,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;QAExB,OAAO,CAAC,IAAI,CAAC;YACX,IAAI,EAAE,UAAU;YAChB,WAAW,EAAE,oBAAoB,UAAU,EAAE;YAC7C,WAAW,EAAE,iBAAiB;SAC/B,CAAC,CAAC;IACL,CAAC;IAED,4BAA4B;IAC5B,MAAM,gBAAgB,GAAG,KAAK,CAAC,cAAc,CAAC,OAAO,GAAG,GAAG,CAAC;SACzD,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;IAExB,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAChC,OAAO,CAAC,IAAI,CAAC;YACX,IAAI,EAAE,UAAU;YAChB,WAAW,EAAE,oBAAoB,OAAO,EAAE;YAC1C,WAAW,EAAE,gBAAgB;SAC9B,CAAC,CAAC;IACL,CAAC;IAED,8CAA8C;IAC9C,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACrB,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAC3B,IAAI,SAAS,EAAE,CAAC;YACd,MAAM,oBAAoB,GAAG,KAAK,CAAC,cAAc,CAAC,SAAS,CAAC;iBACzD,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;iBAC1D,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;YAExB,IAAI,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACpC,OAAO,CAAC,IAAI,CAAC;oBACX,IAAI,EAAE,WAAW;oBACjB,WAAW,EAAE,iBAAiB,SAAS,YAAY;oBACnD,WAAW,EAAE,oBAAoB;iBAClC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,OAAO,CAAC;AACjB,CAAC"}