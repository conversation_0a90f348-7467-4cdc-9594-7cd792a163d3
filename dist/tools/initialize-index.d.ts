/**
 * MCP tool for initializing the translation index from existing files
 */
import { TranslationIndex } from '../core/translation-index.js';
import { TranslationFileWatcher } from '../core/file-watcher.js';
/**
 * Setup the initialize index tool
 */
export declare function setupInitializeIndexTool(server: any, index: TranslationIndex, fileWatcher: TranslationFileWatcher, config: any): void;
//# sourceMappingURL=initialize-index.d.ts.map