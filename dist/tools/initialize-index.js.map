{"version": 3, "file": "initialize-index.js", "sourceRoot": "", "sources": ["../../src/tools/initialize-index.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,EAAE,CAAC,EAAE,MAAM,KAAK,CAAC;AAIxB;;GAEG;AACH,MAAM,UAAU,wBAAwB,CACtC,MAAW,EACX,KAAuB,EACvB,WAAmC,EACnC,MAAW;IAEX,MAAM,CAAC,IAAI,CACT,kBAAkB,EAClB,4EAA4E,EAC5E;QACE,KAAK,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,wDAAwD,CAAC;QACpG,aAAa,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,gDAAgD,CAAC;QACpG,aAAa,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,2CAA2C,CAAC;QAC9F,cAAc,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,sCAAsC,CAAC;KAC3F,EACD,KAAK,EAAE,EAAE,KAAK,EAAE,aAAa,EAAE,aAAa,EAAE,cAAc,EAK3D,EAAE,EAAE;QACH,IAAI,CAAC;YACH,MAAM,gBAAgB,GAAG,KAAK,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC;YAChD,MAAM,iBAAiB,GAAG,KAAK,CAAC,YAAY,EAAE,CAAC;YAE/C,oCAAoC;YACpC,IAAI,gBAAgB,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;gBACnC,OAAO;oBACL,OAAO,EAAE,CAAC;4BACR,IAAI,EAAE,MAAM;4BACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;gCACnB,OAAO,EAAE,IAAI;gCACb,MAAM,EAAE,6BAA6B;gCACrC,gBAAgB;gCAChB,iBAAiB;gCACjB,UAAU,EAAE,wCAAwC;6BACrD,EAAE,IAAI,EAAE,CAAC,CAAC;yBACZ,CAAC;iBACH,CAAC;YACJ,CAAC;YAED,mCAAmC;YACnC,IAAI,aAAa,EAAE,CAAC;gBAClB,IAAI,cAAc,EAAE,CAAC;oBACnB,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;gBACpD,CAAC;gBACD,0EAA0E;gBAC1E,KAAK,MAAM,GAAG,IAAI,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC;oBAClC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gBACpB,CAAC;YACH,CAAC;YAED,uBAAuB;YACvB,IAAI,cAAc,EAAE,CAAC;gBACnB,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC,CAAC;YACjE,CAAC;YAED,MAAM,WAAW,CAAC,eAAe,EAAE,CAAC;YAEpC,uBAAuB;YACvB,MAAM,aAAa,GAAG,KAAK,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC;YAC7C,MAAM,cAAc,GAAG,KAAK,CAAC,YAAY,EAAE,CAAC;YAC5C,MAAM,UAAU,GAAG,aAAa,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC;YAE1E,kCAAkC;YAClC,IAAI,gBAAgB,GAAG,IAAI,CAAC;YAC5B,IAAI,aAAa,EAAE,CAAC;gBAClB,IAAI,CAAC;oBACH,gBAAgB,GAAG,MAAM,KAAK,CAAC,iBAAiB,CAAC;wBAC/C,YAAY,EAAE,MAAM,CAAC,YAAY,IAAI,IAAI;wBACzC,OAAO,EAAE,KAAK;qBACf,CAAC,CAAC;gBACL,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,gBAAgB,GAAG;wBACjB,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,0BAA0B;qBAC3E,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,CAAC;wBACR,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;4BACnB,OAAO,EAAE,IAAI;4BACb,WAAW,EAAE,IAAI;4BACjB,UAAU,EAAE;gCACV,UAAU,EAAE,UAAU;gCACtB,SAAS,EAAE,aAAa;gCACxB,SAAS,EAAE,cAAc;gCACzB,aAAa,EAAE,cAAc,CAAC,MAAM;6BACrC;4BACD,OAAO,EAAE;gCACP,MAAM,EAAE;oCACN,QAAQ,EAAE,gBAAgB;oCAC1B,SAAS,EAAE,iBAAiB;iCAC7B;gCACD,KAAK,EAAE;oCACL,QAAQ,EAAE,aAAa;oCACvB,SAAS,EAAE,cAAc;iCAC1B;6BACF;4BACD,UAAU,EAAE,gBAAgB,CAAC,CAAC,CAAC;gCAC7B,KAAK,EAAE,gBAAgB,CAAC,KAAK;gCAC7B,MAAM,EAAE,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;oCACtC,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,WAAW,IAAI,EAAE,CAAC,CAAC,MAAM;oCACnE,SAAS,EAAE,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC,MAAM;oCAC/D,cAAc,EAAE,CAAC,gBAAgB,CAAC,cAAc,IAAI,EAAE,CAAC,CAAC,MAAM;oCAC9D,gBAAgB,EAAE,CAAC,gBAAgB,CAAC,gBAAgB,IAAI,EAAE,CAAC,CAAC,MAAM;iCACnE;6BACF,CAAC,CAAC,CAAC,IAAI;yBACT,EAAE,IAAI,EAAE,CAAC,CAAC;qBACZ,CAAC;aACH,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,CAAC;wBACR,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;4BACnB,KAAK,EAAE,4BAA4B;4BACnC,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;4BACjE,UAAU,EAAE,uDAAuD;yBACpE,EAAE,IAAI,EAAE,CAAC,CAAC;qBACZ,CAAC;aACH,CAAC;QACJ,CAAC;IACH,CAAC,CACF,CAAC;AACJ,CAAC"}