/**
 * MCP tool for syncing translation index back to files
 */
import { TranslationIndex } from '../core/translation-index.js';
import { TranslationFileWatcher } from '../core/file-watcher.js';
/**
 * Setup the sync translations to files tool
 */
export declare function setupSyncTranslationsToFilesTool(server: any, index: TranslationIndex, fileWatcher: TranslationFileWatcher, config: any): void;
/**
 * Utility functions for file synchronization
 */
export declare class SyncUtils {
    /**
     * Validate translation file format
     */
    static validateTranslationFile(filePath: string): Promise<{
        valid: boolean;
        errors: string[];
    }>;
    /**
     * Compare two translation objects and return differences
     */
    static compareTranslations(before: any, after: any): {
        added: string[];
        removed: string[];
        modified: string[];
    };
    /**
     * Flatten nested object keys to dot notation
     */
    private static flattenKeys;
    /**
     * Get nested value using dot notation
     */
    private static getNestedValue;
}
//# sourceMappingURL=sync-translations-to-files.d.ts.map