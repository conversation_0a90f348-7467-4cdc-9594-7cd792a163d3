/**
 * Safe JSON parsing utilities
 */
/**
 * Result of JSON parsing with metadata
 */
export interface ParseResult {
    /** Parsed JSON data */
    data: any;
    /** Source file path */
    filePath: string;
    /** File size in bytes */
    fileSize: number;
    /** Parse timestamp */
    timestamp: number;
}
/**
 * Safe JSON operations with error handling and metadata
 */
export declare class JsonParser {
    /**
     * Safely parse a JSON file with metadata
     * @param filePath - Path to the JSON file
     * @returns Parse result with metadata
     */
    static parseFile(filePath: string): Promise<ParseResult>;
    /**
     * Safely write JSON to a file with formatting
     * @param filePath - Path to write to
     * @param data - Data to write
     * @param indent - Indentation (default: 2 spaces)
     */
    static writeFile(filePath: string, data: any, indent?: number): Promise<void>;
    /**
     * Deep clone an object
     * @param obj - Object to clone
     * @returns Deep cloned object
     */
    static deepClone<T>(obj: T): T;
    /**
     * Compare two objects for deep equality
     * @param obj1 - First object
     * @param obj2 - Second object
     * @returns True if objects are deeply equal
     */
    static deepEqual(obj1: any, obj2: any): boolean;
    /**
     * Merge two objects deeply
     * @param target - Target object
     * @param source - Source object
     * @returns Merged object
     */
    static deepMerge<T extends Record<string, any>>(target: T, source: Partial<T>): T;
    /**
     * Validate JSON structure against a template
     * @param data - Data to validate
     * @param template - Template to validate against
     * @returns Validation result
     */
    static validateStructure(data: any, template: any): {
        valid: boolean;
        errors: string[];
    };
}
//# sourceMappingURL=json-parser.d.ts.map