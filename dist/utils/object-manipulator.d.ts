/**
 * Object manipulation utilities using dot notation
 */
export declare class ObjectManipulator {
    /**
     * Get a value from nested object using dot notation
     * @param obj - Object to search in
     * @param path - Dot notation path
     * @returns Value at path or undefined
     */
    static getValue(obj: any, path: string): any;
    /**
     * Set a value in nested object using dot notation
     * @param obj - Object to modify
     * @param path - Dot notation path
     * @param value - Value to set
     * @returns Modified object
     */
    static setValue(obj: any, path: string, value: any): any;
    /**
     * Delete a value from nested object using dot notation
     * @param obj - Object to modify
     * @param path - Dot notation path
     * @returns True if value was deleted, false if not found
     */
    static deleteValue(obj: any, path: string): boolean;
    /**
     * Check if a path exists in an object
     * @param obj - Object to check
     * @param path - Dot notation path
     * @returns True if path exists
     */
    static hasPath(obj: any, path: string): boolean;
    /**
     * Get all paths in an object (flattened)
     * @param obj - Object to flatten
     * @param prefix - Prefix for paths
     * @returns Array of all paths
     */
    static getAllPaths(obj: any, prefix?: string): string[];
}
//# sourceMappingURL=object-manipulator.d.ts.map