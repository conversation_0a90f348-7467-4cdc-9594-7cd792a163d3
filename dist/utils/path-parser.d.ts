/**
 * Utilities for parsing and manipulating dot-notation paths
 */
/**
 * Memory-efficient path parsing with caching
 */
export declare class PathParser {
    private static readonly CACHE;
    private static readonly MAX_CACHE_SIZE;
    /**
     * Parse a dot-notation path into segments
     * @param path - The path to parse (e.g., "common.buttons.submit")
     * @returns Array of path segments
     */
    static parse(path: string): string[];
    /**
     * Join path segments into a dot-notation path
     * @param segments - Array of path segments
     * @returns Joined path string
     */
    static join(segments: string[]): string;
    /**
     * Get the parent path of a given path
     * @param path - The path to get parent for
     * @returns Parent path or null if no parent
     */
    static getParent(path: string): string | null;
    /**
     * Get the last segment of a path
     * @param path - The path to get last segment for
     * @returns Last segment
     */
    static getLastSegment(path: string): string;
    /**
     * Check if one path is a child of another
     * @param childPath - The potential child path
     * @param parentPath - The potential parent path
     * @returns True if child<PERSON><PERSON> is a child of parentPath
     */
    static isChildOf(childPath: string, parentPath: string): boolean;
    /**
     * Check if one path is a descendant of another
     * @param descendantPath - The potential descendant path
     * @param ancestorPath - The potential ancestor path
     * @returns True if descendantPath is a descendant of ancestorPath
     */
    static isDescendantOf(descendantPath: string, ancestorPath: string): boolean;
    /**
     * Get all possible parent paths for a given path
     * @param path - The path to get parents for
     * @returns Array of parent paths from immediate to root
     */
    static getAllParents(path: string): string[];
    /**
     * Get the depth of a path (number of segments)
     * @param path - The path to measure
     * @returns Depth of the path
     */
    static getDepth(path: string): number;
    /**
     * Get the common prefix of multiple paths
     * @param paths - Array of paths to find common prefix for
     * @returns Common prefix path or empty string if no common prefix
     */
    static getCommonPrefix(paths: string[]): string;
    /**
     * Normalize a path by removing empty segments and trimming
     * @param path - The path to normalize
     * @returns Normalized path
     */
    static normalize(path: string): string;
    /**
     * Validate that a path is well-formed
     * @param path - The path to validate
     * @returns True if path is valid
     */
    static isValid(path: string): boolean;
    /**
     * Clear the internal cache (useful for testing or memory management)
     */
    static clearCache(): void;
    /**
     * Get cache statistics
     * @returns Object with cache size and hit rate info
     */
    static getCacheStats(): {
        size: number;
        maxSize: number;
    };
}
export { debounce, SortedArray } from './sorted-array.js';
//# sourceMappingURL=path-parser.d.ts.map