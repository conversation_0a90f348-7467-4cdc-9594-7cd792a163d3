/**
 * Binary search utilities for sorted operations
 */
export declare class SortedArray<T> {
    private compareFunc;
    private items;
    constructor(compareFunc: (a: T, b: T) => number);
    /**
     * Insert an item in the correct sorted position
     * @param item - Item to insert
     */
    insert(item: T): void;
    /**
     * Remove an item from the array
     * @param item - Item to remove
     * @returns True if item was found and removed
     */
    remove(item: T): boolean;
    /**
     * Find the index of an item
     * @param item - Item to find
     * @returns Index of item or -1 if not found
     */
    indexOf(item: T): number;
    /**
     * Get all items as an array
     * @returns Copy of the internal array
     */
    toArray(): T[];
    /**
     * Get the number of items
     * @returns Length of the array
     */
    get length(): number;
    /**
     * Clear all items
     */
    clear(): void;
    private binarySearch;
}
/**
 * Debounce utility for file watching
 */
export declare function debounce<T extends (...args: any[]) => any>(func: T, wait: number): (...args: Parameters<T>) => void;
//# sourceMappingURL=sorted-array.d.ts.map