# Current usage (works great):
i18n-mcp --dir ./locales --base-language en

# Additional args needed for IDE integration:
i18n-mcp \
  --dir ./locales \
  --base-language en \
  --src-dir ./src \              # Source code directory to analyze
  --exclude "node_modules,dist" \  # Exclude patterns for code analysis  
  --auto-sync \                  # Auto-write changes back to files
  --generate-types ./src/types/i18n.ts \  # Generate TypeScript types
  --watch-code \                 # Also watch source files for changes
  --project-root ./ \            # Project root for relative paths
  --frameworks "react,vue" \     # Framework-specific analysis
  --key-style "nested"           # nested|flat|camelCase|kebab-case

# Example configuration for your MCP client config:
{
  "i18n-mcp": {
    "command": "node",
    "args": [
      "/Users/<USER>/Projects/i18n-mcp/dist/index.js",
      "--dir", "./locales",
      "--src-dir", "./src", 
      "--base-language", "en",
      "--auto-sync",
      "--generate-types", "./src/types/i18n.ts",
      "--frameworks", "react"
    ],
    "timeout": 600,
    "autoApprove": [
      "search_translation",
      "get_translation_context", 
      "analyze_code_file",
      "suggest_translation_keys"
    ]
  }
}