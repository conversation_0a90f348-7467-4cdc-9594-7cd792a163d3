// src/tools/analyze-code-file.ts
export async function setupAnalyzeCodeTool(server: McpServer, index: TranslationIndex) {
  server.tool(
    'analyze_code_file',
    {
      filePath: z.string().describe('Path to source code file to analyze'),
      extractHardcoded: z.boolean().default(true).describe('Extract hardcoded text'),
      findUsage: z.boolean().default(true).describe('Find translation key usage'),
      frameworks: z.array(z.enum(['react', 'vue', 'svelte', 'angular'])).optional()
    },
    async ({ filePath, extractHardcoded, findUsage, frameworks }) => {
      const analyzer = new CodeAnalyzer(frameworks);
      const result = await analyzer.analyzeFile(filePath, {
        extractHardcoded,
        findUsage,
        translationIndex: index
      });

      return {
        content: [{
          type: 'text',
          text: JSON.stringify({
            filePath,
            hardcodedStrings: result.hardcodedStrings,
            translationUsage: result.translationUsage,
            suggestions: result.suggestions,
            framework: result.detectedFramework
          }, null, 2)
        }]
      };
    }
  );
}

// src/tools/add-translation-smart.ts  
export async function setupAddTranslationSmartTool(server: McpServer, index: TranslationIndex) {
  server.tool(
    'add_translation_smart',
    {
      text: z.string().describe('Text to add as translation'),
      languages: z.record(z.string(), z.string()).describe('Translations by language'),
      suggestedKey: z.string().optional().describe('Suggested key path'),
      context: z.string().optional().describe('Context/component where it will be used'),
      autoGenerateKey: z.boolean().default(true).describe('Auto-generate key if not provided')
    },
    async ({ text, languages, suggestedKey, context, autoGenerateKey }) => {
      let keyPath = suggestedKey;
      
      if (!keyPath && autoGenerateKey) {
        keyPath = generateSmartKey(text, context, index);
      }
      
      if (!keyPath) {
        throw new Error('No key path provided and auto-generation failed');
      }

      // Check for similar existing translations
      const similar = await findSimilarTranslations(text, index);
      
      if (similar.length > 0) {
        return {
          content: [{
            type: 'text',
            text: JSON.stringify({
              warning: 'Similar translations found',
              suggestedKey: keyPath,
              similarTranslations: similar,
              proceed: false
            }, null, 2)
          }]
        };
      }

      // Add the translation
      const operations = Object.entries(languages).map(([lang, value]) => ({
        type: 'set' as const,
        keyPath,
        language: lang,
        value
      }));

      const result = await index.batchUpdate(operations);
      
      return {
        content: [{
          type: 'text',
          text: JSON.stringify({
            success: result.success,
            keyPath,
            addedLanguages: Object.keys(languages),
            errors: result.errors
          }, null, 2)
        }]
      };
    }
  );
}

// src/tools/extract-to-translation.ts
export async function setupExtractToTranslationTool(server: McpServer, index: TranslationIndex) {
  server.tool(
    'extract_to_translation',
    {
      filePath: z.string().describe('Source file containing hardcoded text'),
      textToExtract: z.string().describe('The hardcoded text to extract'),
      targetKey: z.string().optional().describe('Target translation key'),
      replaceInFile: z.boolean().default(false).describe('Replace text in source file'),
      framework: z.enum(['react', 'vue', 'svelte', 'angular']).optional()
    },
    async ({ filePath, textToExtract, targetKey, replaceInFile, framework }) => {
      const extractor = new TranslationExtractor(framework);
      
      // Generate key if not provided
      const key = targetKey || generateSmartKey(textToExtract, filePath, index);
      
      // Add to translations (base language)
      await index.set(key, 'en', textToExtract); // Assuming 'en' as base
      
      // Replace in source file if requested
      let updatedContent: string | null = null;
      if (replaceInFile) {
        updatedContent = await extractor.replaceTextWithTranslation(
          filePath, 
          textToExtract, 
          key
        );
      }
      
      return {
        content: [{
          type: 'text',
          text: JSON.stringify({
            extracted: true,
            key,
            originalText: textToExtract,
            filePath,
            replaced: replaceInFile,
            replacementPattern: extractor.getReplacementPattern(key, framework),
            updatedContent: replaceInFile ? updatedContent : null
          }, null, 2)
        }]
      };
    }
  );
}

// src/tools/get-translation-suggestions.ts  
export async function setupGetTranslationSuggestionsTool(server: McpServer, index: TranslationIndex) {
  server.tool(
    'get_translation_suggestions',
    {
      partial: z.string().describe('Partial translation key for completion'),
      maxSuggestions: z.number().min(1).max(50).default(10),
      includeValues: z.boolean().default(true).describe('Include translation values'),
      preferredLanguage: z.string().default('en').describe('Language for values')
    },
    async ({ partial, maxSuggestions, includeValues, preferredLanguage }) => {
      const suggestions = await index.searchByPrefix(partial);
      const limitedSuggestions = suggestions.slice(0, maxSuggestions);
      
      const results = limitedSuggestions.map(keyPath => {
        const suggestion: any = { keyPath };
        
        if (includeValues) {
          const entry = index.get(keyPath, preferredLanguage);
          if (entry && typeof entry === 'object' && 'value' in entry) {
            suggestion.value = entry.value;
          }
        }
        
        return suggestion;
      });
      
      return {
        content: [{
          type: 'text',
          text: JSON.stringify({
            partial,
            suggestions: results,
            total: suggestions.length,
            showing: limitedSuggestions.length
          }, null, 2)
        }]
      };
    }
  );
}

// src/tools/sync-translations-to-files.ts
export async function setupSyncTranslationsToFilesTool(server: McpServer, index: TranslationIndex, fileWatcher: TranslationFileWatcher) {
  server.tool(
    'sync_translations_to_files',
    {
      languages: z.array(z.string()).optional().describe('Specific languages to sync'),
      dryRun: z.boolean().default(false).describe('Preview changes without writing'),
      backup: z.boolean().default(true).describe('Create backup before writing')
    },
    async ({ languages, dryRun, backup }) => {
      const syncResults = await syncIndexToFiles(index, fileWatcher, {
        languages,
        dryRun,
        backup
      });
      
      return {
        content: [{
          type: 'text',
          text: JSON.stringify({
            synced: !dryRun,
            dryRun,
            backup,
            results: syncResults,
            filesModified: syncResults.filter(r => r.modified).length,
            errors: syncResults.filter(r => r.error).length
          }, null, 2)
        }]
      };
    }
  );
}

// Helper functions
function generateSmartKey(text: string, context: string | undefined, index: TranslationIndex): string {
  // Smart key generation logic
  const baseKey = text
    .toLowerCase()
    .replace(/[^a-z0-9\s]/g, '')
    .replace(/\s+/g, '_')
    .slice(0, 30);
    
  const contextPrefix = context ? extractContextPrefix(context) : 'common';
  let candidate = `${contextPrefix}.${baseKey}`;
  
  // Ensure uniqueness
  let counter = 0;
  while (index.get(candidate)) {
    counter++;
    candidate = `${contextPrefix}.${baseKey}_${counter}`;
  }
  
  return candidate;
}

function extractContextPrefix(context: string): string {
  // Extract meaningful prefix from file path or component name
  const basename = context.split('/').pop()?.replace(/\.(ts|tsx|vue|svelte|js|jsx)$/, '');
  return basename?.toLowerCase() || 'component';
}

async function findSimilarTranslations(text: string, index: TranslationIndex) {
  // Use fuzzy search to find similar existing translations
  const results = await index.search(text, { 
    scope: 'values', 
    maxResults: 5,
    caseSensitive: false 
  });
  
  return results.filter(r => r.score > 0.7); // High similarity threshold
}