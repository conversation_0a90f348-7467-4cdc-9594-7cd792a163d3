# i18n MCP Setup Guide

This guide helps you set up the i18n MCP (Model Context Protocol) server in your project to manage internationalization files.

## Quick Start

### 1. Installation

```bash
# Install globally
npm install -g i18n-mcp

# Or use npx (recommended)
npx i18n-mcp
```

### 2. Basic Usage

The i18n MCP will automatically discover your translation files in common locations:

```bash
# Auto-discover translation directory
npx i18n-mcp

# Specify translation directory explicitly
npx i18n-mcp ./i18n/locales

# Use environment variable
I18N_MCP_DIR=./locales npx i18n-mcp
```

## Configuration

### Automatic Discovery

The MCP automatically searches for translation files in these locations:
- `i18n/locales/`
- `i18n/`
- `locales/`
- `translations/`
- `lang/`
- `languages/`
- `src/i18n/locales/`
- `src/i18n/`
- `src/locales/`
- `src/translations/`
- `public/locales/`
- `assets/i18n/`
- `assets/locales/`

### Manual Configuration

#### Command Line Options

```bash
npx i18n-mcp [options] [directory]

Options:
  --dir, -d <path>          Translation directory path
  --base-lang, -b <lang>    Base language (default: en)
  --debug                   Enable debug logging
  --help, -h                Show help
```

#### Environment Variables

```bash
export I18N_MCP_DIR=/path/to/translations
export I18N_MCP_BASE_LANGUAGE=en
export I18N_MCP_DEBUG=true
```

## Project Structure

### Supported File Structure

```
your-project/
├── i18n/locales/          # ← Recommended location
│   ├── en.json
│   ├── fr.json
│   └── de.json
├── src/
│   └── components/
└── package.json
```

### Translation File Format

Translation files should be valid JSON with nested structure:

```json
{
  "common": {
    "buttons": {
      "save": "Save",
      "cancel": "Cancel",
      "delete": "Delete"
    },
    "messages": {
      "success": "Operation completed successfully",
      "error": "An error occurred"
    }
  },
  "pages": {
    "home": {
      "title": "Welcome",
      "subtitle": "Get started with our application"
    }
  }
}
```

## Integration with IDEs

### Claude Desktop / Cursor

Add to your MCP configuration:

```json
{
  "mcpServers": {
    "i18n": {
      "command": "npx",
      "args": ["i18n-mcp", "./i18n/locales"],
      "env": {
        "I18N_MCP_DEBUG": "false"
      }
    }
  }
}
```

### VS Code

The MCP works with any editor that supports the Model Context Protocol.

## Troubleshooting

### Common Issues

#### 1. "Translation directory not found"

**Problem**: The MCP can't find your translation files.

**Solutions**:
- Ensure translation files exist and are in JSON format
- Specify the directory explicitly: `npx i18n-mcp ./path/to/translations`
- Use environment variable: `I18N_MCP_DIR=./locales npx i18n-mcp`
- Check that files have `.json` extension

#### 2. "MCP not indexing existing files"

**Problem**: The MCP starts but doesn't show existing translations.

**Solutions**:
- Enable debug mode: `npx i18n-mcp --debug`
- Check file permissions
- Ensure JSON files are valid (use `npx i18n-mcp --validate`)

#### 3. "Files not being watched"

**Problem**: Changes to translation files aren't detected.

**Solutions**:
- Check that the directory path is correct
- Ensure files are not in ignored directories (node_modules, .git, etc.)
- Try restarting the MCP server

### Debug Mode

Enable debug logging to see what the MCP is doing:

```bash
npx i18n-mcp --debug ./i18n/locales
```

Debug output shows:
- Directory discovery process
- File processing events
- Index updates
- Error details

## Advanced Configuration

### Framework Integration

The MCP supports framework-specific features:

```bash
# React/Next.js projects
npx i18n-mcp --framework react ./locales

# Vue.js projects  
npx i18n-mcp --framework vue ./i18n

# Angular projects
npx i18n-mcp --framework angular ./assets/i18n
```

### TypeScript Support

Generate TypeScript definitions:

```bash
npx i18n-mcp --generate-types ./src/types/i18n.ts ./locales
```

## Best Practices

1. **Consistent Structure**: Keep the same key structure across all language files
2. **Nested Keys**: Use nested objects for better organization
3. **Descriptive Keys**: Use clear, descriptive key names
4. **Base Language**: Always maintain a complete base language file
5. **Validation**: Regularly validate translation files for consistency

## Getting Help

- Check the debug output: `npx i18n-mcp --debug`
- Validate your JSON files: `npx i18n-mcp --validate`
- Review this setup guide
- Check the project documentation

## Next Steps

Once the MCP is running:
1. Use AI assistants to analyze missing translations
2. Add new translation keys
3. Validate translation consistency
4. Generate TypeScript types
5. Extract hardcoded strings from code
