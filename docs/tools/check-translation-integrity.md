# Translation Integrity Checker

The `check_translation_integrity` tool validates that all translation files have the same structure as the base language file. It performs comprehensive analysis to ensure consistency across all language files.

## Overview

This tool reads translation files directly from the filesystem and compares their structure against a base language file (typically `en.json`). It identifies missing keys, extra keys, type mismatches, and provides detailed recommendations for fixing issues.

## Features

- ✅ **Structure Validation**: Ensures all files have the same key structure
- 🔍 **Missing Key Detection**: Identifies keys present in base language but missing in other files
- 🗑️ **Extra Key Detection**: Finds keys that exist in translation files but not in base language
- 🔄 **Type Checking**: Validates that values have the same type across languages
- 📊 **Completeness Analysis**: Calculates translation completeness percentage
- 💡 **Smart Recommendations**: Provides actionable suggestions for fixing issues
- 🚨 **Error Handling**: Gracefully handles invalid JSON files
- 📈 **Performance Optimized**: Efficient for large translation sets

## Usage

### Basic Usage

```typescript
// Check integrity using default base language (en)
const result = await mcpClient.callTool('check_translation_integrity', {});
```

### Advanced Usage

```typescript
// Check with custom options
const result = await mcpClient.callTool('check_translation_integrity', {
  baseLanguage: 'en',        // Base language to use as source of truth
  includeDetails: true,      // Include detailed analysis for each file
  onlyShowIssues: false,     // Show all files or only files with issues
  checkTypes: true           // Enable type mismatch detection
});
```

## Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `baseLanguage` | string | `'en'` | Base language to use as source of truth |
| `includeDetails` | boolean | `true` | Include detailed analysis for each file |
| `onlyShowIssues` | boolean | `false` | Only show files with issues |
| `checkTypes` | boolean | `true` | Check for type mismatches between languages |

## Response Format

The tool returns a comprehensive analysis with the following structure:

```typescript
{
  "isValid": boolean,           // Overall integrity status
  "baseLanguage": string,       // Base language used for comparison
  "totalFiles": number,         // Number of files checked
  "summary": {
    "totalKeys": number,        // Total keys in base language
    "filesWithIssues": number,  // Number of files with problems
    "totalMissingKeys": number, // Total missing keys across all files
    "totalExtraKeys": number,   // Total extra keys across all files
    "totalTypeMismatches": number // Total type mismatches
  },
  "fileResults": {
    "[language]": {
      "language": string,       // Language code
      "filePath": string,       // Full file path
      "exists": boolean,        // Whether file exists
      "validJson": boolean,     // Whether JSON is valid
      "parseError": string,     // Parse error if JSON invalid
      "stats": {
        "totalKeys": number,    // Keys in this file
        "missingKeys": number,  // Missing keys count
        "extraKeys": number,    // Extra keys count
        "typeMismatches": number, // Type mismatches count
        "completeness": number  // Completeness (0-1)
      },
      "missingKeys": [...],     // Detailed missing key info
      "extraKeys": [...],       // Detailed extra key info
      "typeMismatches": [...],  // Detailed type mismatch info
      "recommendations": [...]  // File-specific recommendations
    }
  },
  "recommendations": [...]      // Global recommendations
}
```

## Examples

### Example 1: Basic Integrity Check

```bash
# Using the MCP tool
check_translation_integrity
```

**Response:**
```json
{
  "isValid": false,
  "baseLanguage": "en",
  "totalFiles": 3,
  "summary": {
    "totalKeys": 27,
    "filesWithIssues": 1,
    "totalMissingKeys": 15,
    "totalExtraKeys": 0,
    "totalTypeMismatches": 0
  },
  "recommendations": [
    "🔧 1 file needs attention",
    "📝 Add 15 missing translations across all files"
  ]
}
```

### Example 2: Detailed Analysis

```bash
# Get detailed analysis for all files
check_translation_integrity --includeDetails=true
```

### Example 3: Only Show Problem Files

```bash
# Only show files with issues
check_translation_integrity --onlyShowIssues=true
```

### Example 4: Custom Base Language

```bash
# Use Spanish as base language
check_translation_integrity --baseLanguage=es
```

## Common Issues and Solutions

### Missing Keys
**Issue**: Keys exist in base language but missing in other files
**Solution**: Add the missing translations to the affected files

```json
// Missing in fr.json
"common.buttons.delete": "Supprimer"
```

### Extra Keys
**Issue**: Keys exist in translation files but not in base language
**Solution**: Remove extra keys or add them to base language if needed

### Type Mismatches
**Issue**: Same key has different types in different files
**Solution**: Ensure consistent types across all files

```json
// Base (en.json): "count": 42
// Wrong (fr.json): "count": "42"
// Correct (fr.json): "count": 42
```

### Invalid JSON
**Issue**: Translation file has syntax errors
**Solution**: Fix JSON syntax errors

## Best Practices

1. **Run Regularly**: Check integrity after adding new translations
2. **Use in CI/CD**: Integrate into your build pipeline
3. **Fix Critical Issues First**: Address invalid JSON and low completeness files first
4. **Maintain Base Language**: Keep your base language file as the single source of truth
5. **Type Consistency**: Ensure values have consistent types across languages

## Integration Examples

### GitHub Actions

```yaml
- name: Check Translation Integrity
  run: |
    npx i18n-mcp --dir ./locales
    # Use MCP client to call check_translation_integrity
```

### Pre-commit Hook

```bash
#!/bin/bash
# Check translation integrity before commit
npx i18n-mcp --dir ./locales
# Call check_translation_integrity tool
```

## Troubleshooting

### Tool Not Found
Ensure the tool is properly registered in your MCP server configuration.

### Permission Errors
Check that the MCP server has read access to your translation directory.

### Large File Performance
For very large translation files, consider using `includeDetails: false` for faster analysis.

## Related Tools

- `validate_structure`: Internal index validation
- `get_stats`: Server and index statistics
- `sync_translations_to_files`: Sync changes to files
