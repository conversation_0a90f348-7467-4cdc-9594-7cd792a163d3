# Unified Add Translation Tool Guide

The `add_translation_smart` tool now supports both **single** and **bulk** translation operations in one unified interface. The tool automatically detects whether you're adding one translation or many based on the parameters you provide.

## Single Translation (Backward Compatible)

### Basic Usage
```json
{
  "tool": "add_translation_smart",
  "arguments": {
    "text": "Setting up...",
    "languages": {
      "en": "Setting up...",
      "de": "Einrichten...",
      "fr": "Configuration..."
    },
    "suggestedKey": "customdomain.buttons.settingUp",
    "context": "CustomDomain component button loading state"
  }
}
```

### Response
```json
{
  "success": true,
  "key": "customdomain.buttons.settingUp",
  "originalText": "Setting up...",
  "addedLanguages": ["en", "de", "fr"],
  "fileWriteResults": {
    "en": { "success": true },
    "de": { "success": true },
    "fr": { "success": true }
  },
  "generatedKey": false,
  "keyStyle": "nested",
  "overwritten": false
}
```

## Bulk Translation (New Feature)

### Basic Bulk Usage
```json
{
  "tool": "add_translation_smart",
  "arguments": {
    "translations": [
      {
        "text": "Save",
        "languages": {
          "en": "Save",
          "de": "Speichern",
          "fr": "Enregistrer"
        },
        "suggestedKey": "common.buttons.save"
      },
      {
        "text": "Cancel",
        "languages": {
          "en": "Cancel",
          "de": "Abbrechen",
          "fr": "Annuler"
        },
        "suggestedKey": "common.buttons.cancel"
      },
      {
        "text": "Delete",
        "languages": {
          "en": "Delete",
          "de": "Löschen",
          "fr": "Supprimer"
        },
        "suggestedKey": "common.buttons.delete"
      }
    ]
  }
}
```

### Bulk Response
```json
{
  "success": true,
  "summary": {
    "total": 3,
    "processed": 3,
    "successful": 3,
    "skipped": 0,
    "failed": 0,
    "errors": 0
  },
  "results": [
    {
      "success": true,
      "key": "common.buttons.save",
      "originalText": "Save",
      "addedLanguages": ["en", "de", "fr"],
      "fileWriteResults": { "en": { "success": true }, "de": { "success": true }, "fr": { "success": true } }
    },
    {
      "success": true,
      "key": "common.buttons.cancel",
      "originalText": "Cancel",
      "addedLanguages": ["en", "de", "fr"],
      "fileWriteResults": { "en": { "success": true }, "de": { "success": true }, "fr": { "success": true } }
    },
    {
      "success": true,
      "key": "common.buttons.delete",
      "originalText": "Delete",
      "addedLanguages": ["en", "de", "fr"],
      "fileWriteResults": { "en": { "success": true }, "de": { "success": true }, "fr": { "success": true } }
    }
  ],
  "performance": {
    "batchSize": 50,
    "totalBatches": 1
  }
}
```

## Advanced Options

### Common Options (Both Single and Bulk)
```json
{
  "tool": "add_translation_smart",
  "arguments": {
    // ... your translations ...
    "autoGenerateKey": true,           // Auto-generate keys if not provided
    "keyStyle": "nested",              // Key naming style: nested, flat, camelCase, kebab-case
    "checkSimilar": false,             // Check for similar existing translations
    "overwrite": false,                // Overwrite existing translations
    "validateStructure": false         // Validate translation structure after adding
  }
}
```

### Bulk-Specific Options
```json
{
  "tool": "add_translation_smart",
  "arguments": {
    "translations": [...],
    "skipOnError": true,               // Skip individual entries on error instead of failing entire batch
    "batchSize": 25                    // Process translations in batches of this size (1-100)
  }
}
```

## Performance Benefits

### Single vs Bulk Performance

**Single Translation (3 separate calls):**
- 3 separate MCP requests
- 3 separate file write operations
- 3 separate validation cycles
- Total time: ~300-500ms

**Bulk Translation (1 call with 3 translations):**
- 1 MCP request
- Batched file operations
- Single validation cycle
- Total time: ~50-100ms

### Recommended Batch Sizes
- **Small batches (1-10 translations)**: Use default batchSize (50)
- **Medium batches (10-50 translations)**: Use batchSize 25-50
- **Large batches (50+ translations)**: Use batchSize 20-30 for better error handling

## Error Handling

### Single Translation Errors
```json
{
  "success": false,
  "key": "invalid.key",
  "skipReason": "Invalid key format for style: nested"
}
```

### Bulk Translation Errors
```json
{
  "success": true,  // Overall success if skipOnError=true
  "summary": {
    "total": 5,
    "successful": 3,
    "failed": 2
  },
  "results": [
    { "success": true, "key": "valid.key1", ... },
    { "success": false, "key": "invalid.key", "skipReason": "Key already exists" },
    { "success": true, "key": "valid.key2", ... }
  ],
  "errors": [
    "Translation 2: Key already exists",
    "Translation 4: Invalid key format"
  ]
}
```

## Migration from Separate Tools

### Before (Multiple Calls)
```javascript
// Old approach - slow
await addTranslation({ text: "Save", languages: {...}, suggestedKey: "save" });
await addTranslation({ text: "Cancel", languages: {...}, suggestedKey: "cancel" });
await addTranslation({ text: "Delete", languages: {...}, suggestedKey: "delete" });
```

### After (Single Call)
```javascript
// New approach - fast
await addTranslation({
  translations: [
    { text: "Save", languages: {...}, suggestedKey: "save" },
    { text: "Cancel", languages: {...}, suggestedKey: "cancel" },
    { text: "Delete", languages: {...}, suggestedKey: "delete" }
  ]
});
```

## Best Practices

### 1. **Use Bulk for Multiple Translations**
When adding more than 2-3 translations, always use the bulk format for better performance.

### 2. **Batch Size Optimization**
- Start with default batchSize (50)
- Reduce if you encounter memory issues
- Increase for very small translations

### 3. **Error Handling Strategy**
- Use `skipOnError: true` for large batches
- Use `skipOnError: false` for critical translations that must all succeed

### 4. **Key Generation**
- Provide `suggestedKey` when possible for better control
- Use `autoGenerateKey: true` for quick prototyping
- Set consistent `keyStyle` across your project

### 5. **File Writing**
- The tool automatically writes to files - no need for separate sync
- Check `fileWriteResults` to ensure all files were updated successfully
- Files are merged with existing content, not overwritten

## Tool Detection Logic

The tool automatically detects the operation type:

```javascript
// Single translation detected when:
if (text && languages && !translations) {
  // Handle as single translation
}

// Bulk translation detected when:
if (translations && translations.length > 0) {
  // Handle as bulk translation
}
```

This unified approach provides maximum flexibility while maintaining backward compatibility and significantly improving performance for bulk operations.
