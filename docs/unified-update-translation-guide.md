# Unified Update Translation Tool Guide

The `update_translation` tool now supports both **single** and **bulk** translation update operations in one unified interface. The tool automatically detects whether you're updating one translation or many based on the parameters you provide.

## Single Translation Update (Backward Compatible)

### Basic Usage
```json
{
  "tool": "update_translation",
  "arguments": {
    "keyPath": "dashboard.website.domainManager",
    "updates": {
      "en": "Domain Manager Pro",
      "de": "Domain-Manager Pro",
      "fr": "Gestionnaire de domaine Pro"
    }
  }
}
```

### Response
```json
{
  "success": true,
  "keyPath": "dashboard.website.domainManager",
  "updatedLanguages": ["en", "de", "fr"],
  "fileWriteResults": {
    "en": { "success": true },
    "de": { "success": true },
    "fr": { "success": true }
  }
}
```

## Bulk Translation Update (New Feature)

### Basic Bulk Usage
```json
{
  "tool": "update_translation",
  "arguments": {
    "translations": [
      {
        "keyPath": "common.buttons.save",
        "updates": {
          "en": "Save Changes",
          "de": "Änderungen speichern",
          "fr": "Enregistrer les modifications"
        }
      },
      {
        "keyPath": "common.buttons.cancel",
        "updates": {
          "en": "Cancel Operation",
          "de": "Vorgang abbrechen",
          "fr": "Annuler l'opération"
        }
      },
      {
        "keyPath": "common.buttons.delete",
        "updates": {
          "en": "Delete Item",
          "de": "Element löschen",
          "fr": "Supprimer l'élément"
        }
      }
    ]
  }
}
```

### Bulk Response
```json
{
  "success": true,
  "summary": {
    "total": 3,
    "processed": 3,
    "successful": 3,
    "skipped": 0,
    "failed": 0,
    "errors": 0
  },
  "results": [
    {
      "success": true,
      "keyPath": "common.buttons.save",
      "updatedLanguages": ["en", "de", "fr"],
      "fileWriteResults": { "en": { "success": true }, "de": { "success": true }, "fr": { "success": true } }
    },
    {
      "success": true,
      "keyPath": "common.buttons.cancel",
      "updatedLanguages": ["en", "de", "fr"],
      "fileWriteResults": { "en": { "success": true }, "de": { "success": true }, "fr": { "success": true } }
    },
    {
      "success": true,
      "keyPath": "common.buttons.delete",
      "updatedLanguages": ["en", "de", "fr"],
      "fileWriteResults": { "en": { "success": true }, "de": { "success": true }, "fr": { "success": true } }
    }
  ],
  "performance": {
    "batchSize": 50,
    "totalBatches": 1
  }
}
```

## Advanced Options

### Common Options (Both Single and Bulk)
```json
{
  "tool": "update_translation",
  "arguments": {
    // ... your updates ...
    "validateStructure": true,          // Validate translation structure before updating
    "writeToFiles": true                // Write updates to actual files (recommended)
  }
}
```

### Bulk-Specific Options
```json
{
  "tool": "update_translation",
  "arguments": {
    "translations": [...],
    "skipOnError": true,               // Skip individual entries on error instead of failing entire batch
    "batchSize": 25                    // Process updates in batches of this size (1-100)
  }
}
```

## Key Differences from Add Translation

### Update vs Add Behavior

**Update Translation:**
- ✅ **Requires existing keys** - will fail if translation key doesn't exist
- ✅ **Modifies existing values** - updates current translations
- ✅ **Validates key existence** - ensures you're updating real translations
- ✅ **Preserves structure** - maintains existing file organization

**Add Translation:**
- ✅ **Creates new keys** - adds translations that don't exist yet
- ✅ **Can generate keys** - auto-generates key paths if needed
- ✅ **Conflict detection** - checks for similar existing translations
- ✅ **Structure building** - creates nested key structures

### When to Use Each Tool

**Use `update_translation` when:**
- Improving existing translation text
- Fixing typos in current translations
- Adding missing language variants to existing keys
- Bulk updating multiple existing translations

**Use `add_translation_smart` when:**
- Adding completely new translation keys
- Extracting hardcoded strings to translations
- Creating new features with new text
- Auto-generating key paths

## Performance Benefits

### Single vs Bulk Performance

**Single Update (3 separate calls):**
- 3 separate MCP requests
- 3 separate file operations
- 3 separate validation cycles
- Total time: ~200-400ms

**Bulk Update (1 call with 3 updates):**
- 1 MCP request
- Batched file operations
- Single validation cycle
- Total time: ~30-80ms

### Recommended Batch Sizes
- **Small batches (1-10 updates)**: Use default batchSize (50)
- **Medium batches (10-50 updates)**: Use batchSize 25-50
- **Large batches (50+ updates)**: Use batchSize 20-30 for better error handling

## Error Handling

### Single Update Errors
```json
{
  "error": "Translation key not found",
  "keyPath": "nonexistent.key",
  "suggestion": "Use add_translation_smart to create new translations"
}
```

### Bulk Update Errors
```json
{
  "success": true,  // Overall success if skipOnError=true
  "summary": {
    "total": 5,
    "successful": 3,
    "failed": 2
  },
  "results": [
    { "success": true, "keyPath": "existing.key1", ... },
    { "success": false, "keyPath": "missing.key", "skipReason": "Translation key not found" },
    { "success": true, "keyPath": "existing.key2", ... }
  ],
  "errors": [
    "Update 2: Translation key not found",
    "Update 4: Failed to update index"
  ]
}
```

## File Writing Control

### Automatic File Writing (Default)
```json
{
  "tool": "update_translation",
  "arguments": {
    "keyPath": "common.save",
    "updates": { "en": "Save" },
    "writeToFiles": true  // Default: true
  }
}
```

### Index-Only Updates (No File Writing)
```json
{
  "tool": "update_translation",
  "arguments": {
    "keyPath": "common.save",
    "updates": { "en": "Save" },
    "writeToFiles": false  // Updates index only
  }
}
```

## Migration Examples

### Before (Multiple Calls)
```javascript
// Old approach - slow
await updateTranslation({ keyPath: "btn.save", updates: { en: "Save", de: "Speichern" } });
await updateTranslation({ keyPath: "btn.cancel", updates: { en: "Cancel", de: "Abbrechen" } });
await updateTranslation({ keyPath: "btn.delete", updates: { en: "Delete", de: "Löschen" } });
```

### After (Single Call)
```javascript
// New approach - fast
await updateTranslation({
  translations: [
    { keyPath: "btn.save", updates: { en: "Save", de: "Speichern" } },
    { keyPath: "btn.cancel", updates: { en: "Cancel", de: "Abbrechen" } },
    { keyPath: "btn.delete", updates: { en: "Delete", de: "Löschen" } }
  ]
});
```

## Best Practices

### 1. **Use Bulk for Multiple Updates**
When updating more than 2-3 translations, always use the bulk format for better performance.

### 2. **Validate Before Large Updates**
```json
{
  "validateStructure": true,  // Recommended for bulk operations
  "writeToFiles": true       // Always write to files unless testing
}
```

### 3. **Error Handling Strategy**
- Use `skipOnError: true` for large batches where some failures are acceptable
- Use `skipOnError: false` for critical updates that must all succeed

### 4. **Batch Size Optimization**
- Start with default batchSize (50)
- Reduce for memory-constrained environments
- Increase for very small, simple updates

### 5. **Key Existence Verification**
- Use `search_translation` to verify keys exist before bulk updates
- Check the `skipReason` in results for failed updates

## Tool Detection Logic

The tool automatically detects the operation type:

```javascript
// Single update detected when:
if (keyPath && updates && !translations) {
  // Handle as single update
}

// Bulk update detected when:
if (translations && translations.length > 0) {
  // Handle as bulk update
}
```

## Integration with Other Tools

### Workflow Example: Update After Search
```javascript
// 1. Search for keys to update
const searchResults = await searchTranslation({ query: "button", scope: "keys" });

// 2. Bulk update found keys
await updateTranslation({
  translations: searchResults.results.map(result => ({
    keyPath: result.keyPath,
    updates: { en: `Updated ${result.value}` }
  }))
});
```

This unified approach provides maximum flexibility while maintaining backward compatibility and significantly improving performance for bulk update operations.
