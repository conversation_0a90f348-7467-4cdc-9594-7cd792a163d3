#!/usr/bin/env node

/**
 * Demo script showing IDE integration features
 */

import { promises as fs } from 'fs';
import { join } from 'path';
import { TranslationMCPServer } from '../src/server/mcp-server.js';
import { CodeAnalyzer } from '../src/core/code-analyzer.js';
import { TranslationExtractor } from '../src/core/translation-extractor.js';
import { TypeGenerator } from '../src/core/type-generator.js';

async function runDemo() {
  console.log('🚀 i18n MCP Server - IDE Integration Demo\n');

  // Create demo directories
  const demoDir = join(process.cwd(), 'demo-output');
  const localesDir = join(demoDir, 'locales');
  const srcDir = join(demoDir, 'src');
  const typesDir = join(demoDir, 'types');

  await fs.mkdir(demoDir, { recursive: true });
  await fs.mkdir(localesDir, { recursive: true });
  await fs.mkdir(srcDir, { recursive: true });
  await fs.mkdir(typesDir, { recursive: true });

  // Create initial translation files
  console.log('📁 Creating initial translation files...');
  
  await fs.writeFile(
    join(localesDir, 'en.json'),
    JSON.stringify({
      common: {
        buttons: {
          submit: 'Submit',
          cancel: 'Cancel',
          save: 'Save'
        },
        messages: {
          success: 'Operation completed successfully',
          error: 'An error occurred'
        }
      },
      auth: {
        login: 'Login',
        logout: 'Logout',
        register: 'Register'
      }
    }, null, 2)
  );

  await fs.writeFile(
    join(localesDir, 'es.json'),
    JSON.stringify({
      common: {
        buttons: {
          submit: 'Enviar',
          cancel: 'Cancelar',
          save: 'Guardar'
        },
        messages: {
          success: 'Operación completada exitosamente',
          error: 'Ocurrió un error'
        }
      },
      auth: {
        login: 'Iniciar sesión',
        logout: 'Cerrar sesión',
        register: 'Registrarse'
      }
    }, null, 2)
  );

  // Create sample React component with hardcoded strings
  console.log('⚛️  Creating sample React component...');
  
  const reactComponent = `import React from 'react';
import { useTranslation } from 'react-i18next';

export function UserDashboard() {
  const { t } = useTranslation();

  const handleSubmit = () => {
    console.log('Form submitted');
  };

  return (
    <div className="dashboard">
      <header>
        <h1>Welcome to your dashboard</h1>
        <p>Manage your account and settings here</p>
      </header>
      
      <main>
        <section>
          <h2>Quick Actions</h2>
          <button onClick={handleSubmit}>
            {t('common.buttons.submit')}
          </button>
          <button>{t('common.buttons.cancel')}</button>
          <button>{t('missing.translation.key')}</button>
        </section>
        
        <section>
          <h3>Account Information</h3>
          <p>Update your profile information below</p>
          <form>
            <label htmlFor="name">Full Name</label>
            <input id="name" type="text" placeholder="Enter your full name" />
            
            <label htmlFor="email">Email Address</label>
            <input id="email" type="email" placeholder="Enter your email" />
            
            <button type="submit">Save Changes</button>
          </form>
        </section>
      </main>
      
      <footer>
        <p>Need help? Contact our support team</p>
      </footer>
    </div>
  );
}`;

  await fs.writeFile(join(srcDir, 'UserDashboard.tsx'), reactComponent);

  // Initialize MCP server with IDE features
  console.log('🔧 Initializing MCP server with IDE features...');
  
  const server = new TranslationMCPServer({
    name: 'demo-server',
    version: '1.0.0',
    translationDir: localesDir,
    baseLanguage: 'en',
    srcDir,
    autoSync: true,
    generateTypes: join(typesDir, 'i18n.ts'),
    watchCode: false,
    frameworks: ['react'],
    keyStyle: 'nested'
  });

  await server.start();

  // Step 1: Analyze the React component
  console.log('\n🔍 Step 1: Analyzing React component for hardcoded strings...');
  
  const analyzer = new CodeAnalyzer(['react']);
  const analysisResult = await analyzer.analyzeFile(join(srcDir, 'UserDashboard.tsx'), {
    extractHardcoded: true,
    findUsage: true,
    translationIndex: server.getIndex(),
    minStringLength: 3
  });

  console.log(`   Detected framework: ${analysisResult.detectedFramework}`);
  console.log(`   Found ${analysisResult.hardcodedStrings.length} hardcoded strings`);
  console.log(`   Found ${analysisResult.translationUsage.length} translation usages`);
  console.log(`   Generated ${analysisResult.suggestions.length} suggestions`);

  // Show hardcoded strings
  console.log('\n   📝 Hardcoded strings found:');
  analysisResult.hardcodedStrings.forEach((str, index) => {
    if (index < 5) { // Show first 5
      console.log(`      Line ${str.line}: "${str.text}" (confidence: ${str.confidence.toFixed(2)})`);
      console.log(`         Suggested key: ${str.suggestedKey}`);
    }
  });

  // Show translation usage
  console.log('\n   🔗 Translation usage:');
  analysisResult.translationUsage.forEach(usage => {
    const status = usage.exists ? '✅' : '❌';
    console.log(`      ${status} Line ${usage.line}: ${usage.keyPath}`);
  });

  // Step 2: Extract hardcoded strings
  console.log('\n🔄 Step 2: Extracting hardcoded strings to translations...');
  
  const extractor = new TranslationExtractor('react');
  const index = server.getIndex();

  // Extract some hardcoded strings
  const extractions = [
    { text: 'Welcome to your dashboard', key: 'dashboard.welcome_title' },
    { text: 'Manage your account and settings here', key: 'dashboard.welcome_subtitle' },
    { text: 'Quick Actions', key: 'dashboard.quick_actions_title' },
    { text: 'Account Information', key: 'dashboard.account_info_title' },
    { text: 'Full Name', key: 'form.full_name_label' },
    { text: 'Enter your full name', key: 'form.full_name_placeholder' },
    { text: 'Save Changes', key: 'common.buttons.save_changes' }
  ];

  for (const extraction of extractions) {
    // Add to English
    await index.set(extraction.key, 'en', extraction.text);
    
    // Add Spanish translations (simplified for demo)
    const spanishTranslations: Record<string, string> = {
      'dashboard.welcome_title': 'Bienvenido a tu panel',
      'dashboard.welcome_subtitle': 'Administra tu cuenta y configuración aquí',
      'dashboard.quick_actions_title': 'Acciones Rápidas',
      'dashboard.account_info_title': 'Información de la Cuenta',
      'form.full_name_label': 'Nombre Completo',
      'form.full_name_placeholder': 'Ingresa tu nombre completo',
      'common.buttons.save_changes': 'Guardar Cambios'
    };
    
    if (spanishTranslations[extraction.key]) {
      await index.set(extraction.key, 'es', spanishTranslations[extraction.key]);
    }

    console.log(`   ✅ Extracted: "${extraction.text}" → ${extraction.key}`);
  }

  // Add the missing translation key
  await index.set('missing.translation.key', 'en', 'Missing Translation');
  await index.set('missing.translation.key', 'es', 'Traducción Faltante');

  // Step 3: Generate TypeScript types
  console.log('\n📝 Step 3: Generating TypeScript types...');
  
  const typeGenerator = new TypeGenerator(index);
  await typeGenerator.generateTypes({
    outputPath: join(typesDir, 'i18n.ts'),
    namespace: 'I18n',
    includeValues: false,
    strict: true,
    baseLanguage: 'en'
  });

  console.log(`   ✅ Types generated: ${join(typesDir, 'i18n.ts')}`);

  // Step 4: Show statistics
  console.log('\n📊 Step 4: Translation statistics...');
  
  const stats = index.getStats();
  console.log(`   Total keys: ${stats.totalKeys}`);
  console.log(`   Languages: ${stats.languages.join(', ')}`);
  console.log(`   Cache hits: ${stats.cacheHits}`);
  console.log(`   Cache misses: ${stats.cacheMisses}`);

  // Step 5: Validate structure
  console.log('\n✅ Step 5: Validating translation structure...');
  
  const validation = await index.validateStructure({
    baseLanguage: 'en',
    autoFix: false
  });

  console.log(`   Structure valid: ${validation.valid}`);
  if (!validation.valid) {
    console.log(`   Missing keys: ${Object.keys(validation.missingKeys || {}).length}`);
    console.log(`   Extra keys: ${Object.keys(validation.extraKeys || {}).length}`);
  }

  // Step 6: Show autocomplete suggestions
  console.log('\n🔍 Step 6: Testing autocomplete suggestions...');
  
  const suggestions = index.searchByPrefix('common.');
  console.log(`   Found ${suggestions.length} suggestions for "common."`);
  suggestions.slice(0, 5).forEach(suggestion => {
    const entry = index.get(suggestion, 'en');
    const value = entry && typeof entry === 'object' && 'value' in entry ? entry.value : '';
    console.log(`      ${suggestion}: "${value}"`);
  });

  // Step 7: Sync back to files
  console.log('\n💾 Step 7: Syncing translations back to files...');
  
  // Get all keys and rebuild the translation files
  const allKeys = index.getKeys();
  const languages = index.getLanguages();

  for (const language of languages) {
    const translations: any = {};
    
    for (const keyPath of allKeys) {
      const entry = index.get(keyPath, language);
      if (entry && typeof entry === 'object' && 'value' in entry) {
        setNestedValue(translations, keyPath, entry.value);
      }
    }

    await fs.writeFile(
      join(localesDir, `${language}.json`),
      JSON.stringify(translations, null, 2)
    );
    
    console.log(`   ✅ Updated ${language}.json with ${allKeys.length} keys`);
  }

  // Clean up
  await server.stop();

  console.log('\n🎉 Demo completed successfully!');
  console.log(`\nDemo files created in: ${demoDir}`);
  console.log('   📁 locales/ - Updated translation files');
  console.log('   📁 src/ - Sample React component');
  console.log('   📁 types/ - Generated TypeScript types');
  
  console.log('\n💡 Next steps:');
  console.log('   1. Review the generated TypeScript types');
  console.log('   2. Update your React component to use the new translation keys');
  console.log('   3. Run the MCP server with --src-dir and --generate-types for live updates');
}

// Helper function to set nested values
function setNestedValue(obj: any, keyPath: string, value: any): void {
  const keys = keyPath.split('.');
  let current = obj;
  
  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    if (!(key in current) || typeof current[key] !== 'object') {
      current[key] = {};
    }
    current = current[key];
  }
  
  current[keys[keys.length - 1]] = value;
}

// Run the demo
if (import.meta.url === `file://${process.argv[1]}`) {
  runDemo().catch(error => {
    console.error('❌ Demo failed:', error);
    process.exit(1);
  });
}

export { runDemo };
