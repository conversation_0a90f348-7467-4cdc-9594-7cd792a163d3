/**
 * Demonstration of the translation integrity checker
 * This script shows how to use the check_translation_integrity tool
 */

import { join } from 'path';
import { TranslationIntegrityChecker } from '../src/tools/check-translation-integrity.js';
import { ServerConfig } from '../src/types/translation.js';

async function demonstrateIntegrityChecker() {
  console.log('🔍 Translation Integrity Checker Demo\n');

  // Configure the checker to use test fixtures
  const config: Required<ServerConfig> = {
    name: 'demo-server',
    version: '1.0.0',
    translationDir: join(process.cwd(), 'test', 'fixtures'),
    baseLanguage: 'en',
    debug: false,
    watchOptions: {
      debounceMs: 100,
      ignored: []
    },
    srcDir: '',
    exclude: [],
    autoSync: false,
    generateTypes: '',
    watchCode: false,
    projectRoot: '',
    frameworks: [],
    keyStyle: 'nested'
  };

  const checker = new TranslationIntegrityChecker(config);

  try {
    console.log('📁 Checking translation files in:', config.translationDir);
    console.log('🌐 Base language:', config.baseLanguage);
    console.log('');

    // Run the integrity check
    const result = await checker.checkIntegrity({
      baseLanguage: 'en',
      includeDetails: true,
      onlyShowIssues: false,
      checkTypes: true
    });

    // Display overall results
    console.log('📊 OVERALL RESULTS');
    console.log('==================');
    console.log(`✅ Valid: ${result.isValid ? 'No' : 'Yes, issues found'}`);
    console.log(`📁 Total files: ${result.totalFiles}`);
    console.log(`🔑 Total keys in base language: ${result.summary.totalKeys}`);
    console.log(`⚠️  Files with issues: ${result.summary.filesWithIssues}`);
    console.log(`❌ Total missing keys: ${result.summary.totalMissingKeys}`);
    console.log(`➕ Total extra keys: ${result.summary.totalExtraKeys}`);
    console.log(`🔄 Total type mismatches: ${result.summary.totalTypeMismatches}`);
    console.log('');

    // Display global recommendations
    console.log('💡 GLOBAL RECOMMENDATIONS');
    console.log('==========================');
    result.recommendations.forEach((rec, index) => {
      console.log(`${index + 1}. ${rec}`);
    });
    console.log('');

    // Display per-file results
    console.log('📄 PER-FILE ANALYSIS');
    console.log('====================');
    
    for (const [language, fileResult] of Object.entries(result.fileResults)) {
      console.log(`\n🌐 ${language.toUpperCase()} (${fileResult.filePath})`);
      console.log(`   Valid JSON: ${fileResult.validJson ? '✅' : '❌'}`);
      
      if (!fileResult.validJson) {
        console.log(`   Parse Error: ${fileResult.parseError}`);
        continue;
      }

      console.log(`   Total keys: ${fileResult.stats.totalKeys}`);
      console.log(`   Missing keys: ${fileResult.stats.missingKeys}`);
      console.log(`   Extra keys: ${fileResult.stats.extraKeys}`);
      console.log(`   Type mismatches: ${fileResult.stats.typeMismatches}`);
      console.log(`   Completeness: ${Math.round(fileResult.stats.completeness * 100)}%`);

      // Show some missing keys as examples
      if (fileResult.missingKeys.length > 0) {
        console.log(`   \n   ❌ Missing keys (showing first 5):`);
        fileResult.missingKeys.slice(0, 5).forEach(key => {
          console.log(`      • ${key.keyPath} (expected: ${JSON.stringify(key.expectedValue)})`);
        });
        if (fileResult.missingKeys.length > 5) {
          console.log(`      ... and ${fileResult.missingKeys.length - 5} more`);
        }
      }

      // Show some extra keys as examples
      if (fileResult.extraKeys.length > 0) {
        console.log(`   \n   ➕ Extra keys (showing first 3):`);
        fileResult.extraKeys.slice(0, 3).forEach(key => {
          console.log(`      • ${key.keyPath} (value: ${JSON.stringify(key.actualValue)})`);
        });
        if (fileResult.extraKeys.length > 3) {
          console.log(`      ... and ${fileResult.extraKeys.length - 3} more`);
        }
      }

      // Show type mismatches
      if (fileResult.typeMismatches.length > 0) {
        console.log(`   \n   🔄 Type mismatches:`);
        fileResult.typeMismatches.forEach(mismatch => {
          console.log(`      • ${mismatch.keyPath}: expected ${mismatch.expectedType}, got ${mismatch.actualType}`);
        });
      }

      // Show recommendations
      if (fileResult.recommendations.length > 0) {
        console.log(`   \n   💡 Recommendations:`);
        fileResult.recommendations.forEach(rec => {
          console.log(`      • ${rec}`);
        });
      }
    }

    console.log('\n🎯 SUMMARY');
    console.log('==========');
    if (result.isValid) {
      console.log('✅ All translation files have consistent structure!');
    } else {
      console.log('⚠️  Translation files need attention. See recommendations above.');
      console.log('\n🚀 Quick fixes:');
      console.log('   1. Add missing translations to incomplete files');
      console.log('   2. Remove extra keys that don\'t exist in base language');
      console.log('   3. Fix type mismatches to match base language types');
      console.log('   4. Ensure all JSON files have valid syntax');
    }

  } catch (error) {
    console.error('❌ Error running integrity check:', error);
  }
}

// Run the demo
if (import.meta.url === `file://${process.argv[1]}`) {
  demonstrateIntegrityChecker().catch(console.error);
}

export { demonstrateIntegrityChecker };
