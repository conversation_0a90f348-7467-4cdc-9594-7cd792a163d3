{"version": "2.1.9", "results": [[":test/integration/end-to-end.test.ts", {"duration": 87.95016699999996, "failed": true}], [":test/core/translation-index.test.ts", {"duration": 38.20958299999995, "failed": false}], [":test/performance/benchmarks.test.ts", {"duration": 514.097042, "failed": true}], [":test/server/mcp-server.test.ts", {"duration": 269.60800000000006, "failed": true}], [":test/core/file-watcher.test.ts", {"duration": 3946.4531660000002, "failed": true}], [":test/integration/cli.test.ts", {"duration": 251.84041600000006, "failed": true}], [":test/tools/advanced-mcp-tools.test.ts", {"duration": 86.68883400000004, "failed": true}], [":test/utils/json-operations.test.ts", {"duration": 121.38270800000004, "failed": true}], [":test/utils/error-types.test.ts", {"duration": 12.358457999999985, "failed": true}], [":test/core/type-generator.test.ts", {"duration": 45.564250000000015, "failed": true}], [":test/integration/ide-workflow.test.ts", {"duration": 83.27679099999995, "failed": true}], [":test/core/translation-extractor.test.ts", {"duration": 18.12175000000002, "failed": true}], [":test/core/code-analyzer.test.ts", {"duration": 32.892165999999975, "failed": true}], [":test/utils/path-parser.test.ts", {"duration": 34.15312499999999, "failed": false}]]}