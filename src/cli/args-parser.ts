/**
 * Command line argument parsing utilities
 */

import { ServerConfig } from '../types/translation.js';
import { resolve } from 'path';

/**
 * Parse command line arguments
 */
export function parseArgs(): Partial<ServerConfig> {
  const args = process.argv.slice(2);
  const config: Partial<ServerConfig> = {};

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    const nextArg = args[i + 1];

    switch (arg) {
      case '--dir':
      case '-d':
        if (nextArg && !nextArg.startsWith('-')) {
          config.translationDir = resolve(nextArg);
          i++;
        }
        break;
      
      case '--base-language':
      case '-b':
        if (nextArg && !nextArg.startsWith('-')) {
          config.baseLanguage = nextArg;
          i++;
        }
        break;
      
      case '--debug':
        config.debug = true;
        break;
      
      case '--name':
      case '-n':
        if (nextArg && !nextArg.startsWith('-')) {
          config.name = nextArg;
          i++;
        }
        break;
      
      case '--version':
      case '-v':
        if (nextArg && !nextArg.startsWith('-')) {
          config.version = nextArg;
          i++;
        }
        break;

      case '--src-dir':
        if (nextArg && !nextArg.startsWith('-')) {
          config.srcDir = resolve(nextArg);
          i++;
        }
        break;

      case '--exclude':
        if (nextArg && !nextArg.startsWith('-')) {
          config.exclude = nextArg.split(',').map(p => p.trim());
          i++;
        }
        break;

      case '--auto-sync':
        config.autoSync = true;
        break;

      case '--generate-types':
        if (nextArg && !nextArg.startsWith('-')) {
          config.generateTypes = resolve(nextArg);
          i++;
        }
        break;

      case '--watch-code':
        config.watchCode = true;
        break;

      case '--project-root':
        if (nextArg && !nextArg.startsWith('-')) {
          config.projectRoot = resolve(nextArg);
          i++;
        }
        break;

      case '--frameworks':
        if (nextArg && !nextArg.startsWith('-')) {
          config.frameworks = nextArg.split(',').map(f => f.trim());
          i++;
        }
        break;

      case '--key-style':
        if (nextArg && !nextArg.startsWith('-')) {
          const validStyles = ['nested', 'flat', 'camelCase', 'kebab-case'];
          if (validStyles.includes(nextArg)) {
            config.keyStyle = nextArg as any;
          } else {
            console.error(`Invalid key style: ${nextArg}. Valid options: ${validStyles.join(', ')}`);
            process.exit(1);
          }
          i++;
        }
        break;

      case '--help':
      case '-h':
        printHelp();
        process.exit(0);
        break;
      
      default:
        if (arg && arg.startsWith('-')) {
          console.error(`Unknown option: ${arg}`);
          printHelp();
          process.exit(1);
        }
        // Assume it's a directory path if no flag is provided
        if (arg && !config.translationDir) {
          config.translationDir = resolve(arg);
        }
        break;
    }
  }

  return config;
}

/**
 * Print help information
 */
export function printHelp(): void {
  console.log(`
i18n MCP Server - High-performance translation file management with IDE integration

Usage: i18n-mcp [options] [translation-directory]

Basic Options:
  -d, --dir <path>           Translation files directory (default: ./locales)
  -b, --base-language <lang> Base language for structure template (default: en)
  -n, --name <name>          Server name (default: i18n-mcp)
  -v, --version <version>    Server version (default: 1.0.0)
  --debug                    Enable debug logging
  -h, --help                 Show this help message

IDE Integration Options:
  --src-dir <path>           Source code directory to analyze
  --exclude <patterns>       Exclude patterns for code analysis (comma-separated)
  --auto-sync                Auto-write changes back to files
  --generate-types <path>    Generate TypeScript types file
  --watch-code               Watch source files for changes
  --project-root <path>      Project root for relative paths
  --frameworks <list>        Framework-specific analysis (react,vue,svelte,angular)
  --key-style <style>        Key naming style (nested|flat|camelCase|kebab-case)

Examples:
  # Basic usage
  i18n-mcp ./locales

  # IDE integration
  i18n-mcp --dir ./locales --src-dir ./src --auto-sync --frameworks react

  # Full IDE setup
  i18n-mcp \\
    --dir ./locales \\
    --src-dir ./src \\
    --exclude "node_modules,dist" \\
    --auto-sync \\
    --generate-types ./src/types/i18n.ts \\
    --watch-code \\
    --frameworks "react,vue" \\
    --key-style "nested"

Environment Variables:
  I18N_MCP_DIR              Translation directory
  I18N_MCP_BASE_LANGUAGE    Base language
  I18N_MCP_DEBUG            Enable debug mode (true/false)
  I18N_MCP_SRC_DIR          Source code directory
  I18N_MCP_AUTO_SYNC        Auto-sync mode (true/false)
`);
}

/**
 * Load configuration from environment variables
 */
export function loadEnvConfig(): Partial<ServerConfig> {
  const config: Partial<ServerConfig> = {};

  if (process.env.I18N_MCP_DIR) {
    config.translationDir = resolve(process.env.I18N_MCP_DIR);
  }

  if (process.env.I18N_MCP_BASE_LANGUAGE) {
    config.baseLanguage = process.env.I18N_MCP_BASE_LANGUAGE;
  }

  if (process.env.I18N_MCP_DEBUG) {
    config.debug = process.env.I18N_MCP_DEBUG.toLowerCase() === 'true';
  }

  if (process.env.I18N_MCP_SRC_DIR) {
    config.srcDir = resolve(process.env.I18N_MCP_SRC_DIR);
  }

  if (process.env.I18N_MCP_AUTO_SYNC) {
    config.autoSync = process.env.I18N_MCP_AUTO_SYNC.toLowerCase() === 'true';
  }

  if (process.env.I18N_MCP_EXCLUDE) {
    config.exclude = process.env.I18N_MCP_EXCLUDE.split(',').map(p => p.trim());
  }

  if (process.env.I18N_MCP_GENERATE_TYPES) {
    config.generateTypes = resolve(process.env.I18N_MCP_GENERATE_TYPES);
  }

  if (process.env.I18N_MCP_WATCH_CODE) {
    config.watchCode = process.env.I18N_MCP_WATCH_CODE.toLowerCase() === 'true';
  }

  if (process.env.I18N_MCP_PROJECT_ROOT) {
    config.projectRoot = resolve(process.env.I18N_MCP_PROJECT_ROOT);
  }

  if (process.env.I18N_MCP_FRAMEWORKS) {
    config.frameworks = process.env.I18N_MCP_FRAMEWORKS.split(',').map(f => f.trim());
  }

  if (process.env.I18N_MCP_KEY_STYLE) {
    const validStyles = ['nested', 'flat', 'camelCase', 'kebab-case'];
    if (validStyles.includes(process.env.I18N_MCP_KEY_STYLE)) {
      config.keyStyle = process.env.I18N_MCP_KEY_STYLE as any;
    }
  }

  return config;
}
