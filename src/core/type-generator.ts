/**
 * TypeScript type generation for translation keys
 */

import { promises as fs } from 'fs';
import { dirname } from 'path';
import { TranslationIndex } from './translation-index.js';

/**
 * Options for type generation
 */
export interface TypeGenerationOptions {
  /** Output file path */
  outputPath: string;
  /** Namespace for the types */
  namespace?: string;
  /** Include value types */
  includeValues?: boolean;
  /** Generate strict types (literal unions) */
  strict?: boolean;
  /** Base language for type inference */
  baseLanguage?: string;
}

/**
 * TypeScript type generator for translation keys
 */
export class TypeGenerator {
  private index: TranslationIndex;

  constructor(index: TranslationIndex) {
    this.index = index;
  }

  /**
   * Generate TypeScript types for translation keys
   */
  async generateTypes(options: TypeGenerationOptions): Promise<void> {
    const {
      outputPath,
      namespace = 'I18n',
      includeValues = false,
      strict = true,
      baseLanguage = 'en'
    } = options;

    try {
      // Ensure output directory exists
      await fs.mkdir(dirname(outputPath), { recursive: true });

      const typeContent = this.buildTypeDefinitions(
        namespace,
        includeValues,
        strict,
        baseLanguage
      );

      await fs.writeFile(outputPath, typeContent, 'utf-8');
    } catch (error) {
      throw new Error(`Failed to generate types: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Build the complete type definition content
   */
  private buildTypeDefinitions(
    namespace: string,
    includeValues: boolean,
    strict: boolean,
    baseLanguage: string
  ): string {
    const keys = this.index.getKeys();
    const languages = this.index.getLanguages();

    const header = this.generateHeader();
    const keyTypes = this.generateKeyTypes(keys, strict);
    const valueTypes = includeValues ? this.generateValueTypes(keys, baseLanguage) : '';
    const namespaceDeclaration = this.generateNamespace(
      namespace,
      keyTypes,
      valueTypes,
      languages,
      strict
    );

    return [header, keyTypes, valueTypes, namespaceDeclaration].filter(Boolean).join('\n\n');
  }

  /**
   * Generate file header with metadata
   */
  private generateHeader(): string {
    const timestamp = new Date().toISOString();
    return `/**
 * Auto-generated translation types
 * Generated at: ${timestamp}
 * 
 * This file is automatically generated by i18n-mcp.
 * Do not edit manually - changes will be overwritten.
 */`;
  }

  /**
   * Generate translation key types
   */
  private generateKeyTypes(keys: string[], strict: boolean): string {
    if (strict) {
      // Generate literal union type
      const keyLiterals = keys.map(key => `  | '${key}'`).join('\n');
      return `/**
 * All available translation keys
 */
export type TranslationKey =
${keyLiterals};`;
    } else {
      // Generate string type with autocomplete hints
      const keyHints = keys.slice(0, 20).map(key => `  | '${key}'`).join('\n');
      return `/**
 * Translation keys (with autocomplete hints)
 */
export type TranslationKey = string & {
  // Autocomplete hints:
${keyHints}
  | (string & {});
};`;
    }
  }

  /**
   * Generate value types based on actual translation values
   */
  private generateValueTypes(keys: string[], baseLanguage: string): string {
    const valueTypes: string[] = [];

    // Analyze value types
    const typeMap = new Map<string, Set<string>>();

    keys.forEach(key => {
      const entry = this.index.get(key, baseLanguage);
      if (entry && typeof entry === 'object' && 'value' in entry) {
        const valueType = this.inferValueType(entry.value);
        if (!typeMap.has(key)) {
          typeMap.set(key, new Set());
        }
        typeMap.get(key)!.add(valueType);
      }
    });

    // Generate interface for translation values
    const valueInterface = Array.from(typeMap.entries())
      .map(([key, types]) => {
        const unionType = Array.from(types).join(' | ');
        return `  '${key}': ${unionType};`;
      })
      .join('\n');

    return `/**
 * Translation value types
 */
export interface TranslationValues {
${valueInterface}
}`;
  }

  /**
   * Generate namespace declaration
   */
  private generateNamespace(
    namespace: string,
    keyTypes: string,
    valueTypes: string,
    languages: string[],
    strict: boolean
  ): string {
    const languageUnion = languages.map(lang => `'${lang}'`).join(' | ');
    
    return `/**
 * ${namespace} namespace with translation utilities
 */
export namespace ${namespace} {
  /** Available languages */
  export type Language = ${languageUnion};
  
  /** Translation key type */
  export type Key = TranslationKey;
  
  ${valueTypes ? '/** Translation value types */\n  export type Values = TranslationValues;\n' : ''}
  
  /** Translation function type */
  export type TranslateFunction = (key: Key, params?: Record<string, any>) => string;
  
  /** Translation object type */
  export interface TranslationObject {
    [K in Key]: string;
  }
  
  /** Nested translation structure */
  export type NestedTranslations = {
    [key: string]: string | NestedTranslations;
  };
}

/**
 * Helper types for translation functions
 */
export interface TranslationHelpers {
  /** Check if a key exists */
  hasKey(key: string): key is TranslationKey;
  
  /** Get all keys */
  getKeys(): TranslationKey[];
  
  /** Get available languages */
  getLanguages(): ${namespace}.Language[];
  
  /** Validate translation completeness */
  validateCompleteness(language: ${namespace}.Language): {
    missing: TranslationKey[];
    extra: string[];
  };
}`;
  }

  /**
   * Infer TypeScript type from value
   */
  private inferValueType(value: any): string {
    if (typeof value === 'string') {
      // Check for interpolation patterns
      if (value.includes('{{') || value.includes('{') || value.includes('%')) {
        return 'string'; // Parameterized string
      }
      return 'string';
    } else if (typeof value === 'number') {
      return 'number';
    } else if (typeof value === 'boolean') {
      return 'boolean';
    } else if (Array.isArray(value)) {
      const elementTypes = value.map(v => this.inferValueType(v));
      const uniqueTypes = [...new Set(elementTypes)];
      return `(${uniqueTypes.join(' | ')})[]`;
    } else if (typeof value === 'object' && value !== null) {
      return 'Record<string, any>';
    }
    return 'any';
  }

  /**
   * Generate nested type structure from translation keys
   */
  private generateNestedTypes(keys: string[]): string {
    const tree = this.buildKeyTree(keys);
    return this.treeToTypeScript(tree, 'TranslationTree');
  }

  /**
   * Build a tree structure from flat keys
   */
  private buildKeyTree(keys: string[]): any {
    const tree: any = {};
    
    keys.forEach(key => {
      const parts = key.split('.');
      let current = tree;
      
      parts.forEach((part, index) => {
        if (index === parts.length - 1) {
          current[part] = 'string';
        } else {
          if (!current[part]) {
            current[part] = {};
          }
          current = current[part];
        }
      });
    });
    
    return tree;
  }

  /**
   * Convert tree structure to TypeScript interface
   */
  private treeToTypeScript(tree: any, typeName: string, depth: number = 0): string {
    const indent = '  '.repeat(depth);
    const properties: string[] = [];
    
    Object.entries(tree).forEach(([key, value]) => {
      if (typeof value === 'string') {
        properties.push(`${indent}  ${key}: string;`);
      } else {
        const nestedTypeName = `${typeName}_${key}`;
        const nestedType = this.treeToTypeScript(value, nestedTypeName, depth + 1);
        properties.push(`${indent}  ${key}: ${nestedTypeName};`);
      }
    });
    
    return `${indent}interface ${typeName} {
${properties.join('\n')}
${indent}}`;
  }

  /**
   * Watch for changes and regenerate types
   */
  async watchAndRegenerate(options: TypeGenerationOptions): Promise<void> {
    // Set up file watcher for translation files
    this.index.on('set', () => {
      this.generateTypes(options).catch(error => {
        console.error('Failed to regenerate types:', error);
      });
    });

    this.index.on('delete', () => {
      this.generateTypes(options).catch(error => {
        console.error('Failed to regenerate types:', error);
      });
    });
  }
}

/**
 * Utility functions for type generation
 */
export class TypeGenerationUtils {
  /**
   * Validate generated types by attempting to compile them
   */
  static async validateTypes(filePath: string): Promise<{ valid: boolean; errors: string[] }> {
    try {
      const content = await fs.readFile(filePath, 'utf-8');
      
      // Basic syntax validation
      const errors: string[] = [];
      
      // Check for common syntax errors
      if (!content.includes('export type TranslationKey')) {
        errors.push('Missing TranslationKey type export');
      }
      
      // Check for balanced braces
      const openBraces = (content.match(/{/g) || []).length;
      const closeBraces = (content.match(/}/g) || []).length;
      if (openBraces !== closeBraces) {
        errors.push('Unbalanced braces in generated types');
      }
      
      return {
        valid: errors.length === 0,
        errors
      };
    } catch (error) {
      return {
        valid: false,
        errors: [`Failed to validate types: ${error instanceof Error ? error.message : 'Unknown error'}`]
      };
    }
  }

  /**
   * Generate JSDoc comments for translation keys
   */
  static generateKeyDocumentation(
    keys: string[],
    index: TranslationIndex,
    baseLanguage: string = 'en'
  ): Record<string, string> {
    const docs: Record<string, string> = {};
    
    keys.forEach(key => {
      const entry = index.get(key, baseLanguage);
      if (entry && typeof entry === 'object' && 'value' in entry) {
        const value = entry.value;
        if (typeof value === 'string' && value.length < 100) {
          docs[key] = `/** ${value} */`;
        } else {
          docs[key] = `/** Translation key: ${key} */`;
        }
      }
    });
    
    return docs;
  }
}
