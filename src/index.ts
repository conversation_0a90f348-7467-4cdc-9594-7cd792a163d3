#!/usr/bin/env node

/**
 * Main entry point for the i18n MCP server
 */

import { TranslationMCPServer } from './server/mcp-server.js';
import { ServerConfig } from './types/translation.js';
import { resolveConfig } from './cli/config-resolver.js';
import { setupGracefulShutdown } from './cli/graceful-shutdown.js';

/**
 * Main function
 */
async function main(): Promise<void> {
  try {
    console.log('🚀 Starting i18n MCP Server...\n');
    
    // Resolve configuration
    const config = await resolveConfig();
    
    if (config.debug) {
      console.log('🔧 Configuration:', JSON.stringify(config, null, 2));
    }
    
    // Create and start server
    const server = new TranslationMCPServer(config);
    
    // Setup graceful shutdown
    setupGracefulShutdown(server);
    
    // Start the server
    await server.start();
    
    // Server is now running and will handle MCP requests via STDIO
    
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

// Run the main function
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch((error) => {
    console.error('❌ Fatal error:', error);
    process.exit(1);
  });
}

export { TranslationMCPServer };
export * from './types/translation.js';
export * from './core/translation-index.js';
export * from './core/file-watcher.js';
