/**
 * MCP tool for intelligently adding translations with conflict detection
 */

import { z } from 'zod';
import { TranslationExtractor, ExtractionUtils } from '../core/translation-extractor.js';
import { TranslationIndex } from '../core/translation-index.js';
import { promises as fs } from 'fs';
import { join } from 'path';

/**
 * Setup the smart add translation tool
 */
export function setupAddTranslationSmartTool(server: any, index: TranslationIndex, config: any) {
  server.tool(
    'add_translation_smart',
    'Intelligently add single or multiple translations with conflict detection and key generation',
    {
      // Single translation format (backward compatible)
      text: z.string().optional().describe('Text to add as translation (base language) - for single translation'),
      languages: z.record(z.string(), z.string()).optional().describe('Translations by language code - for single translation'),
      suggestedKey: z.string().optional().describe('Suggested key path - for single translation'),
      context: z.string().optional().describe('Context/component where it will be used - for single translation'),

      // Bulk translation format
      translations: z.array(z.object({
        text: z.string().describe('Text to add as translation (base language)'),
        languages: z.record(z.string(), z.string()).describe('Translations by language code'),
        suggestedKey: z.string().optional().describe('Suggested key path'),
        context: z.string().optional().describe('Context/component where it will be used')
      })).optional().describe('Array of translations to add - for bulk operations'),

      // Common options
      autoGenerateKey: z.boolean().default(true).describe('Auto-generate key if not provided'),
      keyStyle: z.enum(['nested', 'flat', 'camelCase', 'kebab-case']).optional().describe('Key naming style'),
      checkSimilar: z.boolean().default(true).describe('Check for similar existing translations'),
      overwrite: z.boolean().default(false).describe('Overwrite existing translation if key exists'),
      validateStructure: z.boolean().default(true).describe('Validate structure consistency after adding'),

      // Bulk-specific options
      skipOnError: z.boolean().default(true).describe('Skip individual entries on error instead of failing entire batch (bulk only)'),
      batchSize: z.number().min(1).max(100).default(50).describe('Process translations in batches of this size (bulk only)')
    },
    async ({
      text,
      languages,
      suggestedKey,
      context,
      translations,
      autoGenerateKey,
      keyStyle,
      checkSimilar,
      overwrite,
      validateStructure,
      skipOnError,
      batchSize
    }: {
      text?: string;
      languages?: Record<string, string>;
      suggestedKey?: string;
      context?: string;
      translations?: Array<{
        text: string;
        languages: Record<string, string>;
        suggestedKey?: string;
        context?: string;
      }>;
      autoGenerateKey: boolean;
      keyStyle?: 'nested' | 'flat' | 'camelCase' | 'kebab-case';
      checkSimilar: boolean;
      overwrite: boolean;
      validateStructure: boolean;
      skipOnError: boolean;
      batchSize: number;
    }) => {
      try {
        const extractor = new TranslationExtractor();
        const style = keyStyle || config.keyStyle || 'nested';
        const baseLanguage = config.baseLanguage || 'en';

        // Determine if this is a bulk or single operation
        const isBulkOperation = translations && translations.length > 0;

        if (isBulkOperation) {
          // Handle bulk operation
          return await handleBulkTranslations(
            translations!,
            { autoGenerateKey, keyStyle: style, checkSimilar, overwrite, validateStructure, skipOnError, batchSize },
            extractor,
            index,
            config,
            baseLanguage
          );
        } else {
          // Handle single translation (backward compatible)
          if (!text || !languages) {
            return {
              content: [{
                type: 'text',
                text: JSON.stringify({
                  error: 'For single translation, both text and languages are required',
                  suggestion: 'Provide text and languages, or use translations array for bulk operations'
                }, null, 2)
              }]
            };
          }

          return await handleSingleTranslation(
            { text, languages, suggestedKey, context },
            { autoGenerateKey, keyStyle: style, checkSimilar, overwrite, validateStructure },
            extractor,
            index,
            config,
            baseLanguage
          );
        }
      } catch (error) {
        return {
          content: [{
            type: 'text',
            text: `Error adding translation: ${error instanceof Error ? error.message : 'Unknown error'}`
          }]
        };
      }
    }
  );
}

/**
 * Handle single translation (backward compatible)
 */
async function handleSingleTranslation(
  translation: { text: string; languages: Record<string, string>; suggestedKey?: string; context?: string },
  options: { autoGenerateKey: boolean; keyStyle: string; checkSimilar: boolean; overwrite: boolean; validateStructure: boolean },
  extractor: TranslationExtractor,
  index: TranslationIndex,
  config: any,
  baseLanguage: string
) {
  const result = await processSingleTranslation(
    translation,
    options,
    extractor,
    index,
    config,
    baseLanguage
  );

  return {
    content: [{
      type: 'text',
      text: JSON.stringify(result, null, 2)
    }]
  };
}

/**
 * Handle bulk translations
 */
async function handleBulkTranslations(
  translations: Array<{ text: string; languages: Record<string, string>; suggestedKey?: string; context?: string }>,
  options: { autoGenerateKey: boolean; keyStyle: string; checkSimilar: boolean; overwrite: boolean; validateStructure: boolean; skipOnError: boolean; batchSize: number },
  extractor: TranslationExtractor,
  index: TranslationIndex,
  config: any,
  baseLanguage: string
) {
  const results: any[] = [];
  const errors: string[] = [];
  let processed = 0;
  let successful = 0;
  let skipped = 0;

  // Process translations in batches
  for (let i = 0; i < translations.length; i += options.batchSize) {
    const batch = translations.slice(i, i + options.batchSize);

    for (const translation of batch) {
      try {
        const result = await processSingleTranslation(
          translation,
          options,
          extractor,
          index,
          config,
          baseLanguage
        );

        results.push(result);
        processed++;

        if (result.success) {
          successful++;
        } else if (result.skipped) {
          skipped++;
        }

      } catch (error) {
        const errorMsg = error instanceof Error ? error.message : 'Unknown error';
        errors.push(`Translation ${processed + 1}: ${errorMsg}`);

        if (!options.skipOnError) {
          throw new Error(`Batch failed at translation ${processed + 1}: ${errorMsg}`);
        }

        results.push({
          success: false,
          key: translation.suggestedKey || 'unknown',
          originalText: translation.text,
          addedLanguages: [],
          fileWriteResults: {},
          skipReason: errorMsg
        });

        processed++;
      }
    }
  }

  // Calculate summary statistics
  const summary = {
    total: translations.length,
    processed,
    successful,
    skipped,
    failed: processed - successful - skipped,
    errors: errors.length
  };

  return {
    content: [{
      type: 'text',
      text: JSON.stringify({
        success: errors.length === 0 || options.skipOnError,
        summary,
        results: results.slice(0, 20), // Limit results for readability
        errors: errors.slice(0, 10), // Limit errors for readability
        performance: {
          batchSize: options.batchSize,
          totalBatches: Math.ceil(translations.length / options.batchSize)
        }
      }, null, 2)
    }]
  };
}

/**
 * Process a single translation entry
 */
async function processSingleTranslation(
  translation: { text: string; languages: Record<string, string>; suggestedKey?: string; context?: string },
  options: { autoGenerateKey: boolean; keyStyle: string; checkSimilar: boolean; overwrite: boolean; validateStructure: boolean },
  extractor: TranslationExtractor,
  index: TranslationIndex,
  config: any,
  baseLanguage: string
): Promise<any> {
  // Generate key if not provided
  let keyPath = translation.suggestedKey;
  let generatedKey = false;

  if (!keyPath && options.autoGenerateKey) {
    keyPath = extractor.generateSmartKey(translation.text, translation.context, options.keyStyle as 'nested' | 'flat' | 'camelCase' | 'kebab-case');
    generatedKey = true;
  }

  if (!keyPath) {
    return {
      success: false,
      key: 'unknown',
      originalText: translation.text,
      addedLanguages: [],
      fileWriteResults: {},
      skipped: true,
      skipReason: 'No key path provided and auto-generation failed'
    };
  }

  // Validate key format
  if (!ExtractionUtils.validateKeyFormat(keyPath, options.keyStyle)) {
    return {
      success: false,
      key: keyPath,
      originalText: translation.text,
      addedLanguages: [],
      fileWriteResults: {},
      skipped: true,
      skipReason: `Invalid key format for style: ${options.keyStyle}`
    };
  }

  // Check if key already exists
  if (index.has(keyPath) && !options.overwrite) {
    return {
      success: false,
      key: keyPath,
      originalText: translation.text,
      addedLanguages: [],
      fileWriteResults: {},
      skipped: true,
      skipReason: 'Translation key already exists (use overwrite=true to replace)'
    };
  }

  // Check for similar existing translations
  if (options.checkSimilar && !options.overwrite) {
    const similarTranslations = await ExtractionUtils.findSimilarTranslations(
      translation.text,
      index,
      0.7
    );

    if (similarTranslations.length > 0) {
      return {
        success: false,
        key: keyPath,
        originalText: translation.text,
        addedLanguages: [],
        fileWriteResults: {},
        skipped: true,
        skipReason: `Similar translations found: ${similarTranslations.map(s => s.keyPath).join(', ')}`
      };
    }
  }

  // Prepare all languages (include base language)
  const allLanguages = { ...translation.languages };
  if (!allLanguages[baseLanguage]) {
    allLanguages[baseLanguage] = translation.text;
  }

  // Add the translations to index
  const operations = Object.entries(allLanguages).map(([lang, value]) => ({
    type: 'set' as const,
    keyPath,
    language: lang,
    value
  }));

  const result = await index.batchUpdate(operations);

  if (!result.success) {
    return {
      success: false,
      key: keyPath,
      originalText: translation.text,
      addedLanguages: [],
      fileWriteResults: {},
      skipReason: `Failed to update index: ${result.errors?.join(', ')}`
    };
  }

  // Write translations to files immediately
  const fileWriteResults: Record<string, { success: boolean; error?: string }> = {};
  for (const [language, value] of Object.entries(allLanguages)) {
    try {
      await writeTranslationToFile(language, keyPath, value, config.translationDir, index);
      fileWriteResults[language] = { success: true };
    } catch (error) {
      fileWriteResults[language] = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  return {
    success: true,
    key: keyPath,
    originalText: translation.text,
    addedLanguages: Object.keys(allLanguages),
    fileWriteResults,
    generatedKey,
    keyStyle: options.keyStyle,
    overwritten: options.overwrite && index.has(keyPath)
  };
}

/**
 * Write a single translation to its corresponding file
 */
async function writeTranslationToFile(
  language: string,
  keyPath: string,
  value: any,
  translationDir: string,
  index: TranslationIndex
): Promise<void> {
  const filePath = join(translationDir, `${language}.json`);

  // Read existing file content
  let existingData: any = {};
  try {
    const content = await fs.readFile(filePath, 'utf-8');
    existingData = JSON.parse(content);
  } catch (error) {
    // File doesn't exist or is invalid, start with empty object
    existingData = {};
  }

  // Set the nested value
  setNestedValue(existingData, keyPath, value);

  // Write back to file with proper formatting
  const newContent = JSON.stringify(existingData, null, 2);
  await fs.writeFile(filePath, newContent, 'utf-8');
}

/**
 * Set a nested value in an object using dot notation
 */
function setNestedValue(obj: any, keyPath: string, value: any): void {
  const keys = keyPath.split('.');
  let current = obj;

  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    if (!key) continue; // Skip empty keys

    if (!(key in current) || typeof current[key] !== 'object' || current[key] === null) {
      current[key] = {};
    }
    current = current[key];
  }

  const lastKey = keys[keys.length - 1];
  if (lastKey) {
    current[lastKey] = value;
  }
}
