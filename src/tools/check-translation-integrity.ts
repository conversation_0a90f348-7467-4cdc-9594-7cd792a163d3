/**
 * Translation file integrity checker tool
 * Validates that all translation files have the same structure as the base language
 */

import { z } from 'zod';
import { promises as fs } from 'fs';
import { join, extname } from 'path';
import { 
  TranslationIntegrityResult, 
  FileIntegrityResult, 
  KeyIssue, 
  TypeMismatch,
  ServerConfig 
} from '../types/translation.js';

/**
 * Setup the check translation integrity tool
 */
export function setupCheckTranslationIntegrityTool(
  server: any,
  config: Required<ServerConfig>
): void {
  server.tool(
    'check_translation_integrity',
    'Check integrity of translation files against base language structure',
    {
      baseLanguage: z.string().optional().describe('Base language to use as source of truth (default: en)'),
      includeDetails: z.boolean().default(true).describe('Include detailed analysis for each file'),
      onlyShowIssues: z.boolean().default(false).describe('Only show files with issues'),
      checkTypes: z.boolean().default(true).describe('Check for type mismatches between languages')
    },
    async ({ 
      baseLanguage, 
      includeDetails, 
      onlyShowIssues, 
      checkTypes 
    }: {
      baseLanguage?: string;
      includeDetails: boolean;
      onlyShowIssues: boolean;
      checkTypes: boolean;
    }) => {
      try {
        const checker = new TranslationIntegrityChecker(config);
        const result = await checker.checkIntegrity({
          baseLanguage: baseLanguage || config.baseLanguage,
          includeDetails,
          onlyShowIssues,
          checkTypes
        });

        return {
          content: [{
            type: 'text',
            text: JSON.stringify(result, null, 2)
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: 'text',
            text: `Error checking translation integrity: ${error instanceof Error ? error.message : 'Unknown error'}`
          }]
        };
      }
    }
  );
}

/**
 * Translation integrity checker implementation
 */
export class TranslationIntegrityChecker {
  constructor(private readonly config: Required<ServerConfig>) {}

  /**
   * Check integrity of all translation files
   */
  async checkIntegrity(options: {
    baseLanguage: string;
    includeDetails: boolean;
    onlyShowIssues: boolean;
    checkTypes: boolean;
  }): Promise<TranslationIntegrityResult> {
    const { baseLanguage, includeDetails, onlyShowIssues, checkTypes } = options;

    // Get all translation files
    const translationFiles = await this.getTranslationFiles();
    
    if (translationFiles.length === 0) {
      throw new Error(`No translation files found in ${this.config.translationDir}`);
    }

    // Load base language file
    const baseFilePath = join(this.config.translationDir, `${baseLanguage}.json`);
    const baseData = await this.loadTranslationFile(baseFilePath);
    
    if (!baseData.validJson || !baseData.data) {
      throw new Error(`Base language file ${baseLanguage}.json is invalid or missing`);
    }

    // Get all keys from base language
    const baseKeys = this.extractAllKeys(baseData.data);
    
    // Initialize result
    const result: TranslationIntegrityResult = {
      isValid: true,
      baseLanguage,
      totalFiles: translationFiles.length,
      summary: {
        totalKeys: baseKeys.length,
        filesWithIssues: 0,
        totalMissingKeys: 0,
        totalExtraKeys: 0,
        totalTypeMismatches: 0
      },
      fileResults: {},
      recommendations: []
    };

    // Check each translation file
    for (const file of translationFiles) {
      const language = this.extractLanguageFromFilename(file);
      
      // Skip base language
      if (language === baseLanguage) {
        continue;
      }

      const fileResult = await this.checkSingleFile(
        file, 
        language, 
        baseData.data, 
        baseKeys, 
        checkTypes
      );

      // Update summary
      if (fileResult.stats.missingKeys > 0 || 
          fileResult.stats.extraKeys > 0 || 
          fileResult.stats.typeMismatches > 0 ||
          !fileResult.validJson) {
        result.isValid = false;
        result.summary.filesWithIssues++;
      }

      result.summary.totalMissingKeys += fileResult.stats.missingKeys;
      result.summary.totalExtraKeys += fileResult.stats.extraKeys;
      result.summary.totalTypeMismatches += fileResult.stats.typeMismatches;

      // Add to results if not filtering or if has issues
      if (!onlyShowIssues || fileResult.stats.missingKeys > 0 || 
          fileResult.stats.extraKeys > 0 || fileResult.stats.typeMismatches > 0 ||
          !fileResult.validJson) {
        result.fileResults[language] = includeDetails ? fileResult : this.summarizeFileResult(fileResult);
      }
    }

    // Generate global recommendations
    result.recommendations = this.generateGlobalRecommendations(result);

    return result;
  }

  /**
   * Get all translation files in the directory
   */
  private async getTranslationFiles(): Promise<string[]> {
    try {
      const files = await fs.readdir(this.config.translationDir);
      return files
        .filter(file => extname(file) === '.json')
        .map(file => join(this.config.translationDir, file));
    } catch (error) {
      throw new Error(`Cannot read translation directory: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Load and parse a translation file
   */
  private async loadTranslationFile(filePath: string): Promise<{
    validJson: boolean;
    data?: any;
    parseError?: string;
  }> {
    try {
      const content = await fs.readFile(filePath, 'utf-8');
      const data = JSON.parse(content);
      return { validJson: true, data };
    } catch (error) {
      return { 
        validJson: false, 
        parseError: error instanceof Error ? error.message : 'Unknown parse error' 
      };
    }
  }

  /**
   * Extract language code from filename
   */
  private extractLanguageFromFilename(filePath: string): string {
    const filename = filePath.split('/').pop() || '';
    return filename.replace('.json', '');
  }

  /**
   * Extract all keys from a nested object using dot notation
   */
  private extractAllKeys(obj: any, prefix: string = ''): Array<{ keyPath: string; value: any; type: string; jsonPath: string[] }> {
    const keys: Array<{ keyPath: string; value: any; type: string; jsonPath: string[] }> = [];
    
    if (obj && typeof obj === 'object' && !Array.isArray(obj)) {
      for (const [key, value] of Object.entries(obj)) {
        const keyPath = prefix ? `${prefix}.${key}` : key;
        const jsonPath = prefix ? prefix.split('.').concat(key) : [key];
        
        if (value && typeof value === 'object' && !Array.isArray(value)) {
          // Recursively extract nested keys
          keys.push(...this.extractAllKeys(value, keyPath));
        } else {
          // Leaf node
          keys.push({
            keyPath,
            value,
            type: typeof value,
            jsonPath
          });
        }
      }
    }
    
    return keys;
  }

  /**
   * Get value from nested object using path array
   */
  private getValueByPath(obj: any, path: string[]): any {
    let current = obj;
    for (const key of path) {
      if (current && typeof current === 'object' && key in current) {
        current = current[key];
      } else {
        return undefined;
      }
    }
    return current;
  }

  /**
   * Check a single translation file against the base
   */
  private async checkSingleFile(
    filePath: string,
    language: string,
    baseData: any,
    baseKeys: Array<{ keyPath: string; value: any; type: string; jsonPath: string[] }>,
    checkTypes: boolean
  ): Promise<FileIntegrityResult> {
    const fileData = await this.loadTranslationFile(filePath);
    
    const result: FileIntegrityResult = {
      language,
      filePath,
      exists: true,
      validJson: fileData.validJson,
      parseError: fileData.parseError,
      stats: {
        totalKeys: 0,
        missingKeys: 0,
        extraKeys: 0,
        typeMismatches: 0,
        completeness: 0
      },
      missingKeys: [],
      extraKeys: [],
      typeMismatches: [],
      recommendations: []
    };

    if (!fileData.validJson || !fileData.data) {
      result.recommendations.push(`Fix JSON syntax errors in ${language}.json`);
      return result;
    }

    // Extract keys from this file
    const fileKeys = this.extractAllKeys(fileData.data);
    const fileKeyMap = new Map(fileKeys.map(k => [k.keyPath, k]));
    
    result.stats.totalKeys = fileKeys.length;

    // Check for missing keys
    for (const baseKey of baseKeys) {
      if (!fileKeyMap.has(baseKey.keyPath)) {
        result.missingKeys.push({
          keyPath: baseKey.keyPath,
          expectedValue: baseKey.value,
          expectedType: baseKey.type,
          jsonPath: baseKey.jsonPath
        });
        result.stats.missingKeys++;
      } else if (checkTypes) {
        // Check for type mismatches
        const fileKey = fileKeyMap.get(baseKey.keyPath)!;
        if (baseKey.type !== fileKey.type) {
          result.typeMismatches.push({
            keyPath: baseKey.keyPath,
            expectedType: baseKey.type,
            actualType: fileKey.type,
            expectedValue: baseKey.value,
            actualValue: fileKey.value,
            jsonPath: baseKey.jsonPath
          });
          result.stats.typeMismatches++;
        }
      }
    }

    // Check for extra keys
    const baseKeySet = new Set(baseKeys.map(k => k.keyPath));
    for (const fileKey of fileKeys) {
      if (!baseKeySet.has(fileKey.keyPath)) {
        result.extraKeys.push({
          keyPath: fileKey.keyPath,
          actualValue: fileKey.value,
          actualType: fileKey.type,
          jsonPath: fileKey.jsonPath
        });
        result.stats.extraKeys++;
      }
    }

    // Calculate completeness
    const translatedKeys = baseKeys.length - result.stats.missingKeys;
    result.stats.completeness = baseKeys.length > 0 ? translatedKeys / baseKeys.length : 1;

    // Generate file-specific recommendations
    result.recommendations = this.generateFileRecommendations(result);

    return result;
  }

  /**
   * Generate file-specific recommendations
   */
  private generateFileRecommendations(fileResult: FileIntegrityResult): string[] {
    const recommendations: string[] = [];

    if (!fileResult.validJson) {
      recommendations.push(`Fix JSON syntax errors in ${fileResult.language}.json`);
      return recommendations;
    }

    if (fileResult.stats.missingKeys > 0) {
      recommendations.push(
        `Add ${fileResult.stats.missingKeys} missing translation${fileResult.stats.missingKeys > 1 ? 's' : ''} to ${fileResult.language}.json`
      );

      // Show top 5 missing keys as examples
      const topMissing = fileResult.missingKeys.slice(0, 5);
      if (topMissing.length > 0) {
        recommendations.push(
          `Missing keys include: ${topMissing.map(k => k.keyPath).join(', ')}${fileResult.missingKeys.length > 5 ? '...' : ''}`
        );
      }
    }

    if (fileResult.stats.extraKeys > 0) {
      recommendations.push(
        `Remove ${fileResult.stats.extraKeys} extra key${fileResult.stats.extraKeys > 1 ? 's' : ''} from ${fileResult.language}.json`
      );

      // Show top 5 extra keys as examples
      const topExtra = fileResult.extraKeys.slice(0, 5);
      if (topExtra.length > 0) {
        recommendations.push(
          `Extra keys include: ${topExtra.map(k => k.keyPath).join(', ')}${fileResult.extraKeys.length > 5 ? '...' : ''}`
        );
      }
    }

    if (fileResult.stats.typeMismatches > 0) {
      recommendations.push(
        `Fix ${fileResult.stats.typeMismatches} type mismatch${fileResult.stats.typeMismatches > 1 ? 'es' : ''} in ${fileResult.language}.json`
      );

      // Show top 3 type mismatches as examples
      const topMismatches = fileResult.typeMismatches.slice(0, 3);
      if (topMismatches.length > 0) {
        recommendations.push(
          `Type mismatches: ${topMismatches.map(m => `${m.keyPath} (expected ${m.expectedType}, got ${m.actualType})`).join(', ')}`
        );
      }
    }

    if (fileResult.stats.completeness < 1) {
      const percentage = Math.round(fileResult.stats.completeness * 100);
      recommendations.push(`Translation completeness: ${percentage}% (${fileResult.stats.totalKeys - fileResult.stats.missingKeys}/${fileResult.stats.totalKeys} keys)`);
    }

    return recommendations;
  }

  /**
   * Generate global recommendations
   */
  private generateGlobalRecommendations(result: TranslationIntegrityResult): string[] {
    const recommendations: string[] = [];

    if (result.isValid) {
      recommendations.push('✅ All translation files have consistent structure');
      return recommendations;
    }

    if (result.summary.filesWithIssues > 0) {
      recommendations.push(
        `🔧 ${result.summary.filesWithIssues} file${result.summary.filesWithIssues > 1 ? 's' : ''} need${result.summary.filesWithIssues === 1 ? 's' : ''} attention`
      );
    }

    if (result.summary.totalMissingKeys > 0) {
      recommendations.push(
        `📝 Add ${result.summary.totalMissingKeys} missing translation${result.summary.totalMissingKeys > 1 ? 's' : ''} across all files`
      );
    }

    if (result.summary.totalExtraKeys > 0) {
      recommendations.push(
        `🗑️ Remove ${result.summary.totalExtraKeys} extra key${result.summary.totalExtraKeys > 1 ? 's' : ''} that don't exist in base language`
      );
    }

    if (result.summary.totalTypeMismatches > 0) {
      recommendations.push(
        `⚠️ Fix ${result.summary.totalTypeMismatches} type mismatch${result.summary.totalTypeMismatches > 1 ? 'es' : ''} to match base language types`
      );
    }

    // Priority recommendations
    const criticalFiles = Object.values(result.fileResults).filter(f =>
      !f.validJson || f.stats.completeness < 0.5
    );

    if (criticalFiles.length > 0) {
      recommendations.push(
        `🚨 Priority: Fix ${criticalFiles.length} file${criticalFiles.length > 1 ? 's' : ''} with critical issues (invalid JSON or <50% complete)`
      );
    }

    return recommendations;
  }

  /**
   * Create a summarized version of file result (without detailed arrays)
   */
  private summarizeFileResult(fileResult: FileIntegrityResult): FileIntegrityResult {
    return {
      ...fileResult,
      missingKeys: [], // Remove detailed arrays for summary view
      extraKeys: [],
      typeMismatches: [],
      recommendations: fileResult.recommendations.slice(0, 3) // Keep only top 3 recommendations
    };
  }
}
