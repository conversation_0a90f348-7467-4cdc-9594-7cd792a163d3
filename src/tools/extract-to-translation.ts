/**
 * MCP tool for extracting hardcoded strings to translation files
 */

import { z } from 'zod';
import { TranslationExtractor, ExtractionUtils } from '../core/translation-extractor.js';
import { TranslationIndex } from '../core/translation-index.js';
import { SupportedFramework } from '../types/translation.js';

/**
 * Setup the extract to translation tool
 */
export function setupExtractToTranslationTool(server: any, index: TranslationIndex, config: any) {
  server.tool(
    'extract_to_translation',
    'Extract hardcoded text from source file and replace with translation call',
    {
      filePath: z.string().describe('Source file containing hardcoded text'),
      textToExtract: z.string().describe('The hardcoded text to extract'),
      targetKey: z.string().optional().describe('Target translation key (auto-generated if not provided)'),
      replaceInFile: z.boolean().default(false).describe('Replace text in source file with translation call'),
      framework: z.enum(['react', 'vue', 'svelte', 'angular']).optional().describe('Framework for replacement pattern'),
      baseLanguage: z.string().optional().describe('Base language for the translation'),
      additionalLanguages: z.record(z.string(), z.string()).optional().describe('Additional language translations'),
      checkSimilar: z.boolean().default(true).describe('Check for similar existing translations'),
      keyStyle: z.enum(['nested', 'flat', 'camelCase', 'kebab-case']).optional().describe('Key naming style')
    },
    async ({ 
      filePath, 
      textToExtract, 
      targetKey, 
      replaceInFile, 
      framework,
      baseLanguage,
      additionalLanguages,
      checkSimilar,
      keyStyle
    }: {
      filePath: string;
      textToExtract: string;
      targetKey?: string;
      replaceInFile: boolean;
      framework?: SupportedFramework;
      baseLanguage?: string;
      additionalLanguages?: Record<string, string>;
      checkSimilar: boolean;
      keyStyle?: 'nested' | 'flat' | 'camelCase' | 'kebab-case';
    }) => {
      try {
        const extractor = new TranslationExtractor(framework);
        const baseLang = baseLanguage || config.baseLanguage || 'en';
        const style = keyStyle || config.keyStyle || 'nested';

        // Generate key if not provided
        let key = targetKey;
        if (!key) {
          key = extractor.generateSmartKey(textToExtract, filePath, style);
        }

        // Validate key format
        if (!ExtractionUtils.validateKeyFormat(key, style)) {
          return {
            content: [{
              type: 'text',
              text: JSON.stringify({
                error: 'Invalid key format',
                key,
                expectedFormat: style,
                suggestion: extractor.generateSmartKey(textToExtract, filePath, style)
              }, null, 2)
            }]
          };
        }

        // Check for similar existing translations
        let similarTranslations: any[] = [];
        if (checkSimilar) {
          similarTranslations = await ExtractionUtils.findSimilarTranslations(
            textToExtract,
            index,
            0.7
          );

          if (similarTranslations.length > 0) {
            return {
              content: [{
                type: 'text',
                text: JSON.stringify({
                  warning: 'Similar translations found',
                  suggestedKey: key,
                  textToExtract,
                  similarTranslations,
                  proceed: false,
                  recommendation: 'Review similar translations before proceeding'
                }, null, 2)
              }]
            };
          }
        }

        // Check if key already exists
        if (index.has(key)) {
          const existing = index.get(key, baseLang);
          return {
            content: [{
              type: 'text',
              text: JSON.stringify({
                error: 'Translation key already exists',
                key,
                existingValue: existing && typeof existing === 'object' && 'value' in existing ? existing.value : existing,
                suggestion: `${key}_alt` // Suggest alternative
              }, null, 2)
            }]
          };
        }

        // Add to translations (base language)
        await index.set(key, baseLang, textToExtract);

        // Add additional languages if provided
        const addedLanguages = [baseLang];
        if (additionalLanguages) {
          for (const [lang, value] of Object.entries(additionalLanguages)) {
            await index.set(key, lang, value);
            addedLanguages.push(lang);
          }
        }

        // Replace in source file if requested
        let updatedContent: string | null = null;
        let replacementPattern: string | null = null;
        
        if (replaceInFile) {
          try {
            updatedContent = await extractor.replaceTextWithTranslation(
              filePath, 
              textToExtract, 
              key
            );
            replacementPattern = extractor.getReplacementPattern(key, framework);
          } catch (error) {
            return {
              content: [{
                type: 'text',
                text: JSON.stringify({
                  partialSuccess: true,
                  key,
                  translationAdded: true,
                  fileReplaceError: error instanceof Error ? error.message : 'Unknown error',
                  manualReplacementPattern: extractor.getReplacementPattern(key, framework)
                }, null, 2)
              }]
            };
          }
        }

        return {
          content: [{
            type: 'text',
            text: JSON.stringify({
              success: true,
              key,
              originalText: textToExtract,
              filePath,
              addedLanguages,
              replaced: replaceInFile,
              replacementPattern: replacementPattern || extractor.getReplacementPattern(key, framework),
              detectedFramework: framework,
              keyStyle: style,
              updatedContent: replaceInFile ? (updatedContent ? 'File updated successfully' : null) : null
            }, null, 2)
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: 'text',
            text: `Error extracting translation: ${error instanceof Error ? error.message : 'Unknown error'}`
          }]
        };
      }
    }
  );
}
