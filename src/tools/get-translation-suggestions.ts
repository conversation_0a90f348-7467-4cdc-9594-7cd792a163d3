/**
 * MCP tool for getting translation key suggestions and autocomplete
 */

import { z } from 'zod';
import { TranslationIndex } from '../core/translation-index.js';

/**
 * Setup the translation suggestions tool
 */
export function setupGetTranslationSuggestionsTool(server: any, index: TranslationIndex, config: any) {
  server.tool(
    'get_translation_suggestions',
    'Get translation key suggestions for autocomplete and discovery',
    {
      partial: z.string().describe('Partial translation key for completion'),
      maxSuggestions: z.number().min(1).max(100).default(10).describe('Maximum number of suggestions'),
      includeValues: z.boolean().default(true).describe('Include translation values in results'),
      preferredLanguage: z.string().optional().describe('Language for values (defaults to base language)'),
      includeMetadata: z.boolean().default(false).describe('Include metadata like file paths and line numbers'),
      sortBy: z.enum(['alphabetical', 'usage', 'relevance']).default('relevance').describe('Sort order for suggestions'),
      filterBy: z.object({
        hasValue: z.boolean().optional().describe('Filter keys that have values'),
        language: z.string().optional().describe('Filter keys that exist in specific language'),
        pattern: z.string().optional().describe('Regex pattern to match keys')
      }).optional().describe('Additional filters')
    },
    async ({ 
      partial, 
      maxSuggestions, 
      includeValues, 
      preferredLanguage,
      includeMetadata,
      sortBy,
      filterBy
    }: {
      partial: string;
      maxSuggestions: number;
      includeValues: boolean;
      preferredLanguage?: string;
      includeMetadata: boolean;
      sortBy: 'alphabetical' | 'usage' | 'relevance';
      filterBy?: {
        hasValue?: boolean;
        language?: string;
        pattern?: string;
      };
    }) => {
      try {
        const baseLanguage = preferredLanguage || config.baseLanguage || 'en';
        
        // Get prefix-based suggestions
        let suggestions = index.searchByPrefix(partial);
        
        // Apply filters
        if (filterBy) {
          if (filterBy.hasValue !== undefined) {
            suggestions = suggestions.filter(keyPath => {
              const entry = index.get(keyPath, baseLanguage);
              const hasValue = entry && typeof entry === 'object' && 'value' in entry && entry.value;
              return filterBy.hasValue ? hasValue : !hasValue;
            });
          }
          
          if (filterBy.language) {
            suggestions = suggestions.filter(keyPath => {
              return index.get(keyPath, filterBy.language!) !== null;
            });
          }
          
          if (filterBy.pattern) {
            try {
              const regex = new RegExp(filterBy.pattern);
              suggestions = suggestions.filter(keyPath => regex.test(keyPath));
            } catch (error) {
              // Invalid regex, skip pattern filter
            }
          }
        }
        
        // Sort suggestions
        switch (sortBy) {
          case 'alphabetical':
            suggestions.sort();
            break;
          case 'usage':
            // Sort by key depth (more nested = more specific = higher priority)
            suggestions.sort((a, b) => {
              const depthA = a.split('.').length;
              const depthB = b.split('.').length;
              return depthB - depthA;
            });
            break;
          case 'relevance':
          default:
            // Sort by relevance (exact prefix match first, then by length)
            suggestions.sort((a, b) => {
              const aStartsWithPartial = a.toLowerCase().startsWith(partial.toLowerCase());
              const bStartsWithPartial = b.toLowerCase().startsWith(partial.toLowerCase());
              
              if (aStartsWithPartial && !bStartsWithPartial) return -1;
              if (!aStartsWithPartial && bStartsWithPartial) return 1;
              
              return a.length - b.length;
            });
            break;
        }
        
        // Limit results
        const limitedSuggestions = suggestions.slice(0, maxSuggestions);
        
        // Build result objects
        const results = limitedSuggestions.map(keyPath => {
          const suggestion: any = { 
            keyPath,
            score: calculateRelevanceScore(keyPath, partial)
          };
          
          if (includeValues) {
            const entry = index.get(keyPath, baseLanguage);
            if (entry && typeof entry === 'object' && 'value' in entry) {
              suggestion.value = entry.value;
              suggestion.language = baseLanguage;
            }
            
            // Include all language values if metadata is requested
            if (includeMetadata) {
              const allLanguages = index.getLanguages();
              suggestion.translations = {};
              allLanguages.forEach(lang => {
                const langEntry = index.get(keyPath, lang);
                if (langEntry && typeof langEntry === 'object' && 'value' in langEntry) {
                  suggestion.translations[lang] = langEntry.value;
                }
              });
            }
          }
          
          if (includeMetadata) {
            const entry = index.get(keyPath, baseLanguage);
            if (entry && typeof entry === 'object') {
              suggestion.metadata = {
                file: entry.file,
                line: entry.line,
                column: entry.column,
                lastModified: entry.lastModified
              };
            }
            
            // Add structural information
            const parts = keyPath.split('.');
            suggestion.structure = {
              depth: parts.length,
              parent: parts.length > 1 ? parts.slice(0, -1).join('.') : null,
              key: parts[parts.length - 1],
              namespace: parts.length > 1 ? parts[0] : null
            };
          }
          
          return suggestion;
        });
        
        // Get additional context
        const context = {
          totalMatches: suggestions.length,
          showing: limitedSuggestions.length,
          hasMore: suggestions.length > maxSuggestions,
          searchDepth: partial.split('.').length,
          availableLanguages: index.getLanguages(),
          baseLanguage
        };
        
        // Get related suggestions (sibling keys)
        const relatedSuggestions = getRelatedSuggestions(partial, index, 5);
        
        return {
          content: [{
            type: 'text',
            text: JSON.stringify({
              partial,
              context,
              suggestions: results,
              related: relatedSuggestions,
              filters: filterBy,
              sortedBy: sortBy
            }, null, 2)
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: 'text',
            text: `Error getting translation suggestions: ${error instanceof Error ? error.message : 'Unknown error'}`
          }]
        };
      }
    }
  );
}

/**
 * Calculate relevance score for a suggestion
 */
function calculateRelevanceScore(keyPath: string, partial: string): number {
  const lowerKey = keyPath.toLowerCase();
  const lowerPartial = partial.toLowerCase();
  
  let score = 0;
  
  // Exact prefix match gets highest score
  if (lowerKey.startsWith(lowerPartial)) {
    score += 1.0;
  }
  
  // Contains the partial gets medium score
  else if (lowerKey.includes(lowerPartial)) {
    score += 0.5;
  }
  
  // Adjust score based on length difference
  const lengthDiff = Math.abs(keyPath.length - partial.length);
  score -= lengthDiff * 0.01;
  
  // Prefer shorter, more specific keys
  const depth = keyPath.split('.').length;
  score += depth * 0.1;
  
  return Math.max(0, Math.min(1, score));
}

/**
 * Get related suggestions (sibling keys, parent keys, etc.)
 */
function getRelatedSuggestions(partial: string, index: TranslationIndex, maxResults: number): any[] {
  const parts = partial.split('.');
  const related: any[] = [];
  
  // Get parent key suggestions
  if (parts.length > 1) {
    const parentPath = parts.slice(0, -1).join('.');
    const parentSuggestions = index.searchByPrefix(parentPath)
      .filter(key => key !== partial && key.startsWith(parentPath + '.'))
      .slice(0, maxResults);
    
    related.push({
      type: 'siblings',
      description: `Other keys under ${parentPath}`,
      suggestions: parentSuggestions
    });
  }
  
  // Get child key suggestions
  const childSuggestions = index.searchByPrefix(partial + '.')
    .slice(0, maxResults);
  
  if (childSuggestions.length > 0) {
    related.push({
      type: 'children',
      description: `Child keys under ${partial}`,
      suggestions: childSuggestions
    });
  }
  
  // Get namespace suggestions (same first part)
  if (parts.length > 0) {
    const namespace = parts[0];
    if (namespace) {
      const namespaceSuggestions = index.searchByPrefix(namespace)
        .filter(key => key !== partial && !key.startsWith(partial))
        .slice(0, maxResults);

      if (namespaceSuggestions.length > 0) {
        related.push({
          type: 'namespace',
          description: `Other keys in ${namespace} namespace`,
          suggestions: namespaceSuggestions
        });
      }
    }
  }
  
  return related;
}
