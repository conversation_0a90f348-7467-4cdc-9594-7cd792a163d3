/**
 * MCP tool for initializing the translation index from existing files
 */

import { z } from 'zod';
import { TranslationIndex } from '../core/translation-index.js';
import { TranslationFileWatcher } from '../core/file-watcher.js';

/**
 * Setup the initialize index tool
 */
export function setupInitializeIndexTool(
  server: any, 
  index: TranslationIndex, 
  fileWatcher: TranslationFileWatcher,
  config: any
) {
  server.tool(
    'initialize_index',
    'Initialize the translation index by loading all existing translation files',
    {
      force: z.boolean().default(false).describe('Force re-initialization even if index already has data'),
      clearExisting: z.boolean().default(false).describe('Clear existing index data before loading files'),
      validateFiles: z.boolean().default(true).describe('Validate translation files before loading'),
      reportProgress: z.boolean().default(true).describe('Report detailed progress information')
    },
    async ({ force, clearExisting, validateFiles, reportProgress }: {
      force: boolean;
      clearExisting: boolean;
      validateFiles: boolean;
      reportProgress: boolean;
    }) => {
      try {
        const existingKeyCount = index.getKeys().length;
        const existingLanguages = index.getLanguages();
        
        // Check if initialization is needed
        if (existingKeyCount > 0 && !force) {
          return {
            content: [{
              type: 'text',
              text: JSON.stringify({
                skipped: true,
                reason: 'Index already contains data',
                existingKeyCount,
                existingLanguages,
                suggestion: 'Use force=true to re-initialize anyway'
              }, null, 2)
            }]
          };
        }
        
        // Clear existing data if requested
        if (clearExisting) {
          if (reportProgress) {
            console.log('🧹 Clearing existing index data...');
          }
          // Clear the index (this would need to be implemented in TranslationIndex)
          for (const key of index.getKeys()) {
            index.delete(key);
          }
        }
        
        // Initialize the index
        if (reportProgress) {
          console.log('🔄 Initializing translation index from files...');
        }
        
        await fileWatcher.initializeIndex();
        
        // Get final statistics
        const finalKeyCount = index.getKeys().length;
        const finalLanguages = index.getLanguages();
        const loadedKeys = finalKeyCount - (clearExisting ? 0 : existingKeyCount);
        
        // Validate structure if requested
        let validationResult = null;
        if (validateFiles) {
          try {
            validationResult = await index.validateStructure({
              baseLanguage: config.baseLanguage || 'en',
              autoFix: false
            });
          } catch (error) {
            validationResult = {
              error: error instanceof Error ? error.message : 'Unknown validation error'
            };
          }
        }
        
        return {
          content: [{
            type: 'text',
            text: JSON.stringify({
              success: true,
              initialized: true,
              statistics: {
                keysLoaded: loadedKeys,
                totalKeys: finalKeyCount,
                languages: finalLanguages,
                languageCount: finalLanguages.length
              },
              changes: {
                before: {
                  keyCount: existingKeyCount,
                  languages: existingLanguages
                },
                after: {
                  keyCount: finalKeyCount,
                  languages: finalLanguages
                }
              },
              validation: validationResult ? {
                valid: validationResult.valid,
                issues: validationResult.valid ? null : {
                  missingKeys: Object.keys(validationResult.missingKeys || {}).length,
                  extraKeys: Object.keys(validationResult.extraKeys || {}).length,
                  typeMismatches: (validationResult.typeMismatches || []).length,
                  structuralIssues: (validationResult.structuralIssues || []).length
                }
              } : null
            }, null, 2)
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: 'text',
            text: JSON.stringify({
              error: 'Failed to initialize index',
              message: error instanceof Error ? error.message : 'Unknown error',
              suggestion: 'Check that translation files exist and are valid JSON'
            }, null, 2)
          }]
        };
      }
    }
  );
}
