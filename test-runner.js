#!/usr/bin/env node

/**
 * Test runner script for the i18n MCP server
 * Provides convenient commands for running different types of tests
 */

import { spawn } from 'child_process';
import { existsSync } from 'fs';
import { join } from 'path';

const COLORS = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = (color: keyof typeof COLORS, message: string) => {
  console.log(`${COLORS[color]}${message}${COLORS.reset}`);
};

const runCommand = (command: string, args: string[] = [], options: any = {}) => {
  return new Promise<number>((resolve, reject) => {
    log('blue', `Running: ${command} ${args.join(' ')}`);
    
    const child = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
      ...options
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve(code);
      } else {
        reject(new Error(`Command failed with code ${code}`));
      }
    });

    child.on('error', (error) => {
      reject(error);
    });
  });
};

const checkDependencies = () => {
  const packageJsonPath = join(process.cwd(), 'package.json');
  if (!existsSync(packageJsonPath)) {
    log('red', 'Error: package.json not found. Please run from project root.');
    process.exit(1);
  }

  const nodeModulesPath = join(process.cwd(), 'node_modules');
  if (!existsSync(nodeModulesPath)) {
    log('yellow', 'Warning: node_modules not found. Run "npm install" first.');
  }
};

const printUsage = () => {
  log('bright', 'i18n MCP Test Runner');
  console.log('');
  log('cyan', 'Usage: node test-runner.js <command> [options]');
  console.log('');
  log('bright', 'Commands:');
  console.log('  all              Run all tests');
  console.log('  unit             Run unit tests only');
  console.log('  integration      Run integration tests only');
  console.log('  performance      Run performance tests only');
  console.log('  coverage         Run tests with coverage report');
  console.log('  watch            Run tests in watch mode');
  console.log('  ui               Run tests with UI');
  console.log('  build            Build the project before testing');
  console.log('  lint             Run linting checks');
  console.log('  type-check       Run TypeScript type checking');
  console.log('  clean            Clean test artifacts');
  console.log('  help             Show this help message');
  console.log('');
  log('bright', 'Examples:');
  console.log('  node test-runner.js all');
  console.log('  node test-runner.js unit --verbose');
  console.log('  node test-runner.js watch --filter="TranslationIndex"');
  console.log('  node test-runner.js coverage --reporter=html');
  console.log('');
  log('bright', 'Environment Variables:');
  console.log('  TEST_DEBUG=true  Enable debug output in tests');
  console.log('  CI=true          Run in CI mode (no interactive features)');
  console.log('');
};

const commands = {
  async all() {
    log('green', '🧪 Running all tests...');
    await runCommand('npm', ['run', 'test:run']);
  },

  async unit() {
    log('green', '🔬 Running unit tests...');
    await runCommand('npm', ['run', 'test:run', '--', 'test/**/*.test.ts', '!test/integration/**', '!test/performance/**']);
  },

  async integration() {
    log('green', '🔗 Running integration tests...');
    await runCommand('npm', ['run', 'test:run', '--', 'test/integration/**/*.test.ts']);
  },

  async performance() {
    log('green', '⚡ Running performance tests...');
    await runCommand('npm', ['run', 'test:run', '--', 'test/performance/**/*.test.ts']);
  },

  async coverage() {
    log('green', '📊 Running tests with coverage...');
    await runCommand('npm', ['run', 'test:coverage']);
  },

  async watch() {
    log('green', '👀 Running tests in watch mode...');
    await runCommand('npm', ['run', 'test:watch']);
  },

  async ui() {
    log('green', '🎨 Starting test UI...');
    await runCommand('npm', ['run', 'test:ui']);
  },

  async build() {
    log('green', '🏗️ Building project...');
    await runCommand('npm', ['run', 'build']);
  },

  async lint() {
    log('green', '🔍 Running linting checks...');
    await runCommand('npm', ['run', 'lint']);
  },

  async 'type-check'() {
    log('green', '📝 Running TypeScript type checking...');
    await runCommand('npm', ['run', 'typecheck']);
  },

  async clean() {
    log('green', '🧹 Cleaning test artifacts...');
    await runCommand('rm', ['-rf', 'coverage', 'dist', '.vitest']);
    log('green', 'Cleaned coverage, dist, and .vitest directories');
  },

  async ci() {
    log('green', '🚀 Running CI test suite...');
    
    // Set CI environment
    process.env.CI = 'true';
    
    try {
      log('blue', 'Step 1: Type checking...');
      await commands['type-check']();
      
      log('blue', 'Step 2: Building...');
      await commands.build();
      
      log('blue', 'Step 3: Running tests with coverage...');
      await commands.coverage();
      
      log('green', '✅ CI test suite completed successfully!');
    } catch (error) {
      log('red', '❌ CI test suite failed!');
      throw error;
    }
  },

  async benchmark() {
    log('green', '📈 Running performance benchmarks...');
    
    // Run performance tests with special configuration
    await runCommand('npm', ['run', 'test:run', '--', 'test/performance/**/*.test.ts', '--reporter=verbose']);
    
    log('green', 'Benchmark completed! Check output above for performance metrics.');
  },

  async 'test-specific'() {
    const testPattern = process.argv[3];
    if (!testPattern) {
      log('red', 'Error: Please specify a test pattern');
      log('yellow', 'Usage: node test-runner.js test-specific <pattern>');
      log('yellow', 'Example: node test-runner.js test-specific TranslationIndex');
      process.exit(1);
    }

    log('green', `🎯 Running specific tests matching: ${testPattern}`);
    await runCommand('npm', ['run', 'test:run', '--', '--grep', testPattern]);
  },

  async debug() {
    log('green', '🐛 Running tests in debug mode...');
    
    // Set debug environment
    process.env.TEST_DEBUG = 'true';
    process.env.NODE_OPTIONS = '--inspect-brk';
    
    await runCommand('npm', ['run', 'test:run', '--', '--no-coverage']);
  },

  async help() {
    printUsage();
  }
};

// Additional utility commands
const utilityCommands = {
  async 'generate-fixtures'() {
    log('green', '📁 Generating test fixtures...');
    
    const { TestDataGenerator } = await import('./test-utils.js');
    const { join } = await import('path');
    const { promises: fs } = await import('fs');
    
    const fixturesDir = join(process.cwd(), 'test', 'fixtures');
    
    // Generate various test data
    await TestDataGenerator.generateTranslationFiles(fixturesDir, {
      languages: ['en', 'es', 'fr', 'de', 'it', 'ja', 'zh'],
      categories: 10,
      itemsPerCategory: 20,
      includeNested: true
    });
    
    // Generate large dataset for performance testing
    const largeDataset = TestDataGenerator.generateLargeDataset(10000);
    await fs.writeFile(
      join(fixturesDir, 'large-dataset.json'),
      JSON.stringify(largeDataset, null, 2)
    );
    
    log('green', '✅ Test fixtures generated successfully!');
  },

  async 'validate-config'() {
    log('green', '🔧 Validating test configuration...');
    
    const configPath = join(process.cwd(), 'vitest.config.ts');
    if (!existsSync(configPath)) {
      log('red', 'Error: vitest.config.ts not found');
      process.exit(1);
    }
    
    // Check test setup
    const setupPath = join(process.cwd(), 'test', 'setup.ts');
    if (!existsSync(setupPath)) {
      log('red', 'Error: test/setup.ts not found');
      process.exit(1);
    }
    
    log('green', '✅ Test configuration is valid!');
  },

  async 'create-test'() {
    const testName = process.argv[3];
    if (!testName) {
      log('red', 'Error: Please specify a test name');
      log('yellow', 'Usage: node test-runner.js create-test <TestName>');
      process.exit(1);
    }

    log('green', `📝 Creating test template for: ${testName}`);
    
    const testTemplate = `/**
 * Unit tests for ${testName}
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
// import { ${testName} } from '../../src/path/to/${testName.toLowerCase()}.js';

describe('${testName}', () => {
  beforeEach(() => {
    // Setup before each test
  });

  afterEach(() => {
    // Cleanup after each test
  });

  describe('constructor', () => {
    it('should initialize correctly', () => {
      // Test initialization
      expect(true).toBe(true);
    });
  });

  describe('main functionality', () => {
    it('should work as expected', () => {
      // Test main functionality
      expect(true).toBe(true);
    });
  });

  describe('error handling', () => {
    it('should handle errors gracefully', () => {
      // Test error scenarios
      expect(true).toBe(true);
    });
  });
});
`;

    const { promises: fs } = await import('fs');
    const testPath = join(process.cwd(), 'test', `${testName.toLowerCase()}.test.ts`);
    
    await fs.writeFile(testPath, testTemplate);
    log('green', `✅ Test template created: ${testPath}`);
  }
};

// Combine all commands
const allCommands = { ...commands, ...utilityCommands };

async function main() {
  const command = process.argv[2];

  if (!command || command === 'help') {
    printUsage();
    return;
  }

  checkDependencies();

  if (!(command in allCommands)) {
    log('red', `Unknown command: ${command}`);
    log('yellow', 'Run "node test-runner.js help" for available commands');
    process.exit(1);
  }

  try {
    await allCommands[command as keyof typeof allCommands]();
    log('green', `✅ Command "${command}" completed successfully!`);
  } catch (error) {
    log('red', `❌ Command "${command}" failed:`);
    console.error(error);
    process.exit(1);
  }
}

// Handle SIGINT (Ctrl+C) gracefully
process.on('SIGINT', () => {
  log('yellow', '\n🛑 Test runner interrupted');
  process.exit(0);
});

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch((error) => {
    log('red', '💥 Fatal error:');
    console.error(error);
    process.exit(1);
  });
}

export { allCommands as commands };
