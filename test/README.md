# i18n MCP Server - Test Suite

This directory contains the comprehensive test suite for the i18n MCP server project. The tests are designed to ensure reliability, performance, and correctness of all components.

## 📁 Test Structure

```
test/
├── setup.ts                    # Global test setup and utilities
├── test-utils.ts               # Test helpers, mocks, and utilities
├── fixtures/                   # Test data files
│   ├── en.json                 # English translations
│   ├── es.json                 # Spanish translations
│   └── fr.json                 # French translations
├── utils/                      # Utility function tests
│   ├── path-parser.test.ts     # Path parsing and manipulation
│   ├── json-operations.test.ts # JSON file operations
│   └── error-types.test.ts     # Custom error classes
├── core/                       # Core component tests
│   ├── translation-index.test.ts    # Translation indexing engine
│   └── file-watcher.test.ts          # File monitoring system
├── server/                     # Server component tests
│   └── mcp-server.test.ts      # MCP server implementation
├── integration/                # Integration tests
│   ├── end-to-end.test.ts      # Complete workflow tests
│   └── cli.test.ts            # Command-line interface tests
└── performance/                # Performance and benchmarks
    └── benchmarks.test.ts      # Performance testing
```

## 🚀 Quick Start

### Install Dependencies

```bash
npm install
```

### Run All Tests

```bash
npm test
```

### Run Specific Test Types

```bash
# Unit tests only
npm run test:run test/**/*.test.ts !test/integration/** !test/performance/**

# Integration tests
npm run test:run test/integration/**

# Performance tests
npm run test:run test/performance/**

# With coverage
npm run test:coverage

# Watch mode
npm run test:watch

# UI mode
npm run test:ui
```

### Using the Test Runner

We provide a convenient test runner script for common operations:

```bash
# Run all tests
node test-runner.js all

# Run specific test types
node test-runner.js unit
node test-runner.js integration
node test-runner.js performance

# Run with coverage
node test-runner.js coverage

# Watch mode
node test-runner.js watch

# CI mode (type-check + build + test + coverage)
node test-runner.js ci

# Debug mode
node test-runner.js debug

# Clean test artifacts
node test-runner.js clean

# See all available commands
node test-runner.js help
```

## 📋 Test Categories

### Unit Tests

Located in `test/utils/`, `test/core/`, and `test/server/`. These test individual components in isolation.

**What they cover:**
- Path parsing and manipulation utilities
- JSON operations and file handling
- Translation indexing with caching and search
- File watching and change detection
- MCP server setup and tool registration
- Error handling and custom error types

**Key features tested:**
- ✅ O(1) translation lookups with LRU caching
- ✅ Binary search for prefix operations
- ✅ Batch operations with rollback capability
- ✅ Structure validation across languages
- ✅ File watching with debouncing
- ✅ Event-driven architecture

### Integration Tests

Located in `test/integration/`. These test complete workflows and component interactions.

**What they cover:**
- End-to-end translation management workflows
- Real file system operations
- MCP tool interactions
- CLI argument parsing and configuration
- Error propagation and recovery
- Multi-language translation scenarios

**Key workflows tested:**
- ✅ Loading translation files from disk
- ✅ Real-time file change detection and processing
- ✅ Search across multiple languages
- ✅ Structure validation and auto-fixing
- ✅ Batch updates and atomic operations
- ✅ Server startup and graceful shutdown

### Performance Tests

Located in `test/performance/`. These ensure the system performs well under load.

**What they cover:**
- Large dataset handling (10k+ translations)
- Search performance benchmarks
- Memory usage and garbage collection
- Concurrent operation handling
- Cache efficiency under pressure
- File processing speed

**Performance targets:**
- ✅ Handle 10,000 translations in < 2 seconds
- ✅ Search 10k+ keys in < 100ms
- ✅ Prefix search in < 50ms
- ✅ Batch operations (1000 items) in < 500ms
- ✅ Memory usage < 1KB per translation
- ✅ Structure validation in < 1 second

## 🛠 Test Utilities

### Global Test Utils (`globalThis.testUtils`)

Available in all tests through the global setup:

```typescript
// Create temporary directory
const tempDir = await globalThis.testUtils.createTempDir();

// Cleanup temporary directory
await globalThis.testUtils.cleanupTempDir(tempDir);

// Create test translation files
await globalThis.testUtils.createTestTranslationFiles(tempDir);

// Sleep utility
await globalThis.testUtils.sleep(100);
```

### Test Utilities Module (`test-utils.ts`)

Comprehensive utilities for testing:

```typescript
import { 
  TestDataGenerator, 
  TestEnvironment, 
  PerformanceTestUtils,
  AsyncTestUtils,
  AssertionUtils 
} from './test-utils.js';

// Generate realistic test data
const translations = TestDataGenerator.generateTranslationData({
  languages: ['en', 'es', 'fr'],
  categories: 5,
  itemsPerCategory: 10
});

// Create complete test project
const project = await TestEnvironment.createTranslationProject(baseDir);

// Performance testing
const { time, result } = await PerformanceTestUtils.measureTime(async () => {
  return await someOperation();
});

// Wait for events
const eventData = await AsyncTestUtils.waitForEvent(emitter, 'change');

// Assert structure
AssertionUtils.assertStructure(data, expectedStructure);
```

### Mock Implementations

Pre-built mocks for external dependencies:

```typescript
import { 
  MockMcpServer, 
  MockFileWatcher, 
  mockImplementations 
} from './test-utils.js';

// Mock the MCP SDK
mockImplementations.mockMcpSdk();

// Mock file system
const mockFs = mockImplementations.mockFileSystem();

// Mock process methods
const { mockExit, restore } = mockImplementations.mockProcess();
```

## 📊 Coverage Requirements

We maintain high test coverage standards:

- **Overall Coverage**: > 90%
- **Lines**: > 90%
- **Functions**: > 80%
- **Branches**: > 80%
- **Statements**: > 90%

Run coverage reports:

```bash
npm run test:coverage
```

View HTML coverage report:
```bash
open coverage/index.html
```

## 🔧 Configuration

### Vitest Configuration (`vitest.config.ts`)

- **Environment**: Node.js
- **Global Setup**: Automatic test utilities
- **Coverage Provider**: v8
- **Timeout**: 10 seconds
- **Concurrency**: 5 parallel tests

### Environment Variables

```bash
# Enable debug output in tests
TEST_DEBUG=true

# Run in CI mode (no interactive features)
CI=true

# Custom test timeout
VITEST_TIMEOUT=15000
```

## 🏗 Writing New Tests

### Test Template

Use the test runner to create new test files:

```bash
node test-runner.js create-test MyComponent
```

This creates a template with the standard structure:

```typescript
/**
 * Unit tests for MyComponent
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';

describe('MyComponent', () => {
  beforeEach(() => {
    // Setup before each test
  });

  afterEach(() => {
    // Cleanup after each test
  });

  describe('constructor', () => {
    it('should initialize correctly', () => {
      // Test initialization
    });
  });

  describe('main functionality', () => {
    it('should work as expected', () => {
      // Test main functionality
    });
  });

  describe('error handling', () => {
    it('should handle errors gracefully', () => {
      // Test error scenarios
    });
  });
});
```

### Best Practices

1. **Descriptive Test Names**: Use clear, descriptive names that explain what is being tested
2. **Arrange-Act-Assert**: Structure tests with clear setup, execution, and verification phases
3. **Isolation**: Each test should be independent and not rely on other tests
4. **Cleanup**: Always clean up resources (files, event listeners, etc.) in `afterEach`
5. **Mock External Dependencies**: Use mocks for file system, network, and other external dependencies
6. **Performance Awareness**: Include performance assertions for time-critical operations
7. **Error Testing**: Test both success and failure scenarios
8. **Edge Cases**: Test boundary conditions and edge cases

### Example Test

```typescript
describe('TranslationIndex.search', () => {
  let index: TranslationIndex;

  beforeEach(() => {
    index = new TranslationIndex({ baseLanguage: 'en' });
    
    // Setup test data
    index.set('common.buttons.submit', 'en', 'Submit');
    index.set('common.buttons.cancel', 'en', 'Cancel');
    index.set('auth.login.title', 'en', 'Login');
  });

  afterEach(() => {
    index.removeAllListeners();
  });

  it('should find translations by key prefix', async () => {
    // Act
    const results = await index.search('common.buttons', { scope: 'keys' });

    // Assert
    expect(results).toHaveLength(2);
    expect(results[0].keyPath).toBe('common.buttons.submit');
    expect(results[1].keyPath).toBe('common.buttons.cancel');
  });

  it('should limit results when maxResults is specified', async () => {
    // Act
    const results = await index.search('common', { 
      scope: 'keys', 
      maxResults: 1 
    });

    // Assert
    expect(results).toHaveLength(1);
  });

  it('should handle empty search results', async () => {
    // Act
    const results = await index.search('nonexistent', { scope: 'keys' });

    // Assert
    expect(results).toHaveLength(0);
  });
});
```

## 🐛 Debugging Tests

### Debug Mode

Run tests with debugging enabled:

```bash
node test-runner.js debug
```

Or manually:

```bash
NODE_OPTIONS="--inspect-brk" npm run test:run --no-coverage
```

### Debug Output

Enable debug logging in tests:

```bash
TEST_DEBUG=true npm test
```

### IDE Integration

Most IDEs support Vitest debugging. Set up your IDE to:
1. Run tests with Node.js debugging
2. Set breakpoints in test files
3. Use the `--inspect-brk` flag for debugging

## 📈 Performance Testing

### Running Benchmarks

```bash
node test-runner.js benchmark
```

### Performance Assertions

Use performance utilities in tests:

```typescript
import { PerformanceTestUtils } from './test-utils.js';

it('should perform operation quickly', async () => {
  const { time } = await PerformanceTestUtils.measureTime(async () => {
    return await expensiveOperation();
  });
  
  expect(time).toBeLessThan(100); // Should complete in under 100ms
});
```

### Memory Testing

```typescript
it('should not leak memory', () => {
  const initialMemory = process.memoryUsage().heapUsed;
  
  // Perform operations that might leak memory
  for (let i = 0; i < 1000; i++) {
    performOperation();
  }
  
  // Force garbage collection if available
  if (global.gc) global.gc();
  
  const finalMemory = process.memoryUsage().heapUsed;
  const memoryIncrease = finalMemory - initialMemory;
  
  expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024); // Less than 10MB
});
```

## 🔄 Continuous Integration

### GitHub Actions

The test suite is designed to work well in CI environments:

```bash
# Run full CI test suite
node test-runner.js ci
```

This command:
1. Runs TypeScript type checking
2. Builds the project
3. Runs all tests with coverage
4. Generates coverage reports

### Test Artifacts

Tests generate several artifacts:
- **Coverage Reports**: `coverage/` directory
- **Test Results**: JSON reports for CI integration
- **Performance Metrics**: Logged to console
- **Build Artifacts**: `dist/` directory

## 🔍 Troubleshooting

### Common Issues

1. **Tests timing out**: Increase timeout or check for infinite loops
2. **Memory leaks**: Ensure proper cleanup in `afterEach` hooks
3. **File system issues**: Use proper temporary directories and cleanup
4. **Mock issues**: Verify mocks are properly setup and restored

### Debug Commands

```bash
# Check test configuration
node test-runner.js validate-config

# Clean all test artifacts
node test-runner.js clean

# Generate fresh test fixtures
node test-runner.js generate-fixtures

# Run specific test pattern
node test-runner.js test-specific "TranslationIndex"
```

## 📚 Additional Resources

- [Vitest Documentation](https://vitest.dev/)
- [Testing Best Practices](https://testing-library.com/docs/guiding-principles)
- [Performance Testing Guide](https://web.dev/performance-testing/)
- [Mocking Strategies](https://jestjs.io/docs/manual-mocks)

---

Happy testing! 🧪✨
