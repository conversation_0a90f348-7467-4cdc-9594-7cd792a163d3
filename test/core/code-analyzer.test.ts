/**
 * Tests for CodeAnalyzer
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { CodeAnalyzer } from '../../src/core/code-analyzer.js';
import { TranslationIndex } from '../../src/core/translation-index.js';
import { SupportedFramework } from '../../src/types/translation.js';

describe('CodeAnalyzer', () => {
  let analyzer: CodeAnalyzer;
  let mockIndex: TranslationIndex;

  beforeEach(() => {
    analyzer = new CodeAnalyzer(['react', 'vue']);
    mockIndex = new TranslationIndex({
      baseLanguage: 'en',
      cacheSize: 100
    });
    
    // Add some test translations
    mockIndex.set('common.submit', 'en', 'Submit');
    mockIndex.set('common.cancel', 'en', 'Cancel');
    mockIndex.set('auth.login', 'en', 'Login');
  });

  describe('framework detection', () => {
    it('should detect React framework from JSX file', async () => {
      const reactCode = `
        import React from 'react';
        import { useTranslation } from 'react-i18next';
        
        export function Button() {
          const { t } = useTranslation();
          return <button>{t('common.submit')}</button>;
        }
      `;

      // Mock fs.readFile
      vi.doMock('fs', () => ({
        promises: {
          readFile: vi.fn().mockResolvedValue(reactCode)
        }
      }));

      const result = await analyzer.analyzeFile('Button.tsx');
      expect(result.detectedFramework).toBe('react');
    });

    it('should detect Vue framework from .vue file', async () => {
      const vueCode = `
        <template>
          <button>{{ $t('common.submit') }}</button>
        </template>
        
        <script>
        import { useI18n } from 'vue-i18n';
        export default {
          setup() {
            const { t } = useI18n();
            return { t };
          }
        };
        </script>
      `;

      vi.doMock('fs', () => ({
        promises: {
          readFile: vi.fn().mockResolvedValue(vueCode)
        }
      }));

      const result = await analyzer.analyzeFile('Component.vue');
      expect(result.detectedFramework).toBe('vue');
    });
  });

  describe('hardcoded string extraction', () => {
    it('should extract hardcoded strings from React JSX', async () => {
      const reactCode = `
        export function Welcome() {
          return (
            <div>
              <h1>Welcome to our app</h1>
              <button title="Click to submit">Submit Form</button>
              <p>Please fill out the form below.</p>
            </div>
          );
        }
      `;

      vi.doMock('fs', () => ({
        promises: {
          readFile: vi.fn().mockResolvedValue(reactCode)
        }
      }));

      const result = await analyzer.analyzeFile('Welcome.tsx', {
        extractHardcoded: true,
        findUsage: false
      });

      expect(result.hardcodedStrings).toHaveLength(3);
      expect(result.hardcodedStrings.map(s => s.text)).toContain('Welcome to our app');
      expect(result.hardcodedStrings.map(s => s.text)).toContain('Click to submit');
      expect(result.hardcodedStrings.map(s => s.text)).toContain('Please fill out the form below.');
    });

    it('should calculate confidence scores correctly', async () => {
      const code = `
        const messages = {
          userFacing: "Hello, welcome!",
          constant: "API_ENDPOINT",
          short: "OK",
          withNumbers: "Error 404"
        };
      `;

      vi.doMock('fs', () => ({
        promises: {
          readFile: vi.fn().mockResolvedValue(code)
        }
      }));

      const result = await analyzer.analyzeFile('test.js', {
        extractHardcoded: true,
        findUsage: false
      });

      const userFacing = result.hardcodedStrings.find(s => s.text === 'Hello, welcome!');
      const constant = result.hardcodedStrings.find(s => s.text === 'API_ENDPOINT');
      
      expect(userFacing?.confidence).toBeGreaterThan(0.7);
      expect(constant?.confidence).toBeLessThan(0.5);
    });

    it('should generate suggested keys', async () => {
      const code = `
        <h1>User Profile Settings</h1>
      `;

      vi.doMock('fs', () => ({
        promises: {
          readFile: vi.fn().mockResolvedValue(code)
        }
      }));

      const result = await analyzer.analyzeFile('UserProfile.tsx', {
        extractHardcoded: true,
        findUsage: false
      });

      const hardcoded = result.hardcodedStrings.find(s => s.text === 'User Profile Settings');
      expect(hardcoded?.suggestedKey).toMatch(/^react\.user_profile_settings$/);
    });
  });

  describe('translation usage detection', () => {
    it('should find React translation usage', async () => {
      const reactCode = `
        import { useTranslation } from 'react-i18next';
        
        export function App() {
          const { t } = useTranslation();
          return (
            <div>
              <h1>{t('common.submit')}</h1>
              <p>{t('missing.key')}</p>
              <button>{t('auth.login')}</button>
            </div>
          );
        }
      `;

      vi.doMock('fs', () => ({
        promises: {
          readFile: vi.fn().mockResolvedValue(reactCode)
        }
      }));

      const result = await analyzer.analyzeFile('App.tsx', {
        extractHardcoded: false,
        findUsage: true,
        translationIndex: mockIndex
      });

      expect(result.translationUsage).toHaveLength(3);
      
      const submitUsage = result.translationUsage.find(u => u.keyPath === 'common.submit');
      const missingUsage = result.translationUsage.find(u => u.keyPath === 'missing.key');
      const loginUsage = result.translationUsage.find(u => u.keyPath === 'auth.login');

      expect(submitUsage?.exists).toBe(true);
      expect(missingUsage?.exists).toBe(false);
      expect(loginUsage?.exists).toBe(true);
    });

    it('should find Vue translation usage', async () => {
      const vueCode = `
        <template>
          <div>
            <h1>{{ $t('common.submit') }}</h1>
            <p>{{ $t('nonexistent.key') }}</p>
          </div>
        </template>
      `;

      vi.doMock('fs', () => ({
        promises: {
          readFile: vi.fn().mockResolvedValue(vueCode)
        }
      }));

      const result = await analyzer.analyzeFile('Component.vue', {
        extractHardcoded: false,
        findUsage: true,
        translationIndex: mockIndex
      });

      expect(result.translationUsage).toHaveLength(2);
      
      const submitUsage = result.translationUsage.find(u => u.keyPath === 'common.submit');
      const missingUsage = result.translationUsage.find(u => u.keyPath === 'nonexistent.key');

      expect(submitUsage?.exists).toBe(true);
      expect(missingUsage?.exists).toBe(false);
    });
  });

  describe('suggestions generation', () => {
    it('should suggest extracting high-confidence hardcoded strings', async () => {
      const code = `
        <div>
          <h1>Welcome to our application</h1>
          <button>Click here to continue</button>
        </div>
      `;

      vi.doMock('fs', () => ({
        promises: {
          readFile: vi.fn().mockResolvedValue(code)
        }
      }));

      const result = await analyzer.analyzeFile('test.tsx');

      const extractSuggestions = result.suggestions.filter(s => s.type === 'extract');
      expect(extractSuggestions.length).toBeGreaterThan(0);
      expect(extractSuggestions[0].severity).toBe('warning');
    });

    it('should suggest fixing missing translation keys', async () => {
      const code = `
        const message = t('missing.translation.key');
      `;

      vi.doMock('fs', () => ({
        promises: {
          readFile: vi.fn().mockResolvedValue(code)
        }
      }));

      const result = await analyzer.analyzeFile('test.js', {
        translationIndex: mockIndex
      });

      const missingSuggestions = result.suggestions.filter(s => s.type === 'missing-key');
      expect(missingSuggestions.length).toBeGreaterThan(0);
      expect(missingSuggestions[0].severity).toBe('error');
    });
  });

  describe('options handling', () => {
    it('should respect minimum string length option', async () => {
      const code = `
        <div>
          <span>Hi</span>
          <p>This is a longer message</p>
        </div>
      `;

      vi.doMock('fs', () => ({
        promises: {
          readFile: vi.fn().mockResolvedValue(code)
        }
      }));

      const result = await analyzer.analyzeFile('test.tsx', {
        minStringLength: 10
      });

      const shortString = result.hardcodedStrings.find(s => s.text === 'Hi');
      const longString = result.hardcodedStrings.find(s => s.text === 'This is a longer message');

      expect(shortString).toBeUndefined();
      expect(longString).toBeDefined();
    });

    it('should respect exclude patterns', async () => {
      const code = `
        const config = {
          apiUrl: "https://api.example.com",
          message: "Welcome user"
        };
      `;

      vi.doMock('fs', () => ({
        promises: {
          readFile: vi.fn().mockResolvedValue(code)
        }
      }));

      const result = await analyzer.analyzeFile('test.js', {
        excludePatterns: [/https?:\/\//]
      });

      const urlString = result.hardcodedStrings.find(s => s.text.includes('https://'));
      const messageString = result.hardcodedStrings.find(s => s.text === 'Welcome user');

      expect(urlString).toBeUndefined();
      expect(messageString).toBeDefined();
    });
  });

  describe('error handling', () => {
    it('should handle file read errors gracefully', async () => {
      vi.doMock('fs', () => ({
        promises: {
          readFile: vi.fn().mockRejectedValue(new Error('File not found'))
        }
      }));

      await expect(analyzer.analyzeFile('nonexistent.js')).rejects.toThrow('Failed to analyze file');
    });

    it('should handle malformed code gracefully', async () => {
      const malformedCode = `
        const incomplete = {
          missing: "closing brace"
      `;

      vi.doMock('fs', () => ({
        promises: {
          readFile: vi.fn().mockResolvedValue(malformedCode)
        }
      }));

      // Should not throw, but may have fewer results
      const result = await analyzer.analyzeFile('malformed.js');
      expect(result).toBeDefined();
      expect(result.hardcodedStrings).toBeDefined();
      expect(result.translationUsage).toBeDefined();
      expect(result.suggestions).toBeDefined();
    });
  });
});
