/**
 * Unit tests for TranslationFileWatcher
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { TranslationFileWatcher, FileWatcherConfig } from '../../src/core/file-watcher.js';
import { TranslationIndex } from '../../src/core/translation-index.js';
import { promises as fs } from 'fs';
import { join } from 'path';

describe('TranslationFileWatcher', () => {
  let tempDir: string;
  let index: TranslationIndex;
  let watcher: TranslationFileWatcher;
  let config: FileWatcherConfig;

  beforeEach(async () => {
    tempDir = await globalThis.testUtils.createTempDir();
    index = new TranslationIndex({ baseLanguage: 'en' });
    
    config = {
      translationDir: tempDir,
      debounceMs: 50, // Faster for tests
      ignored: ['**/node_modules/**'],
      debug: false
    };
    
    watcher = new TranslationFileWatcher(config, index);
  });

  afterEach(async () => {
    try {
      await watcher.stop();
    } catch (error) {
      // Ignore stop errors in tests
    }
    watcher.removeAllListeners();
    await globalThis.testUtils.cleanupTempDir(tempDir);
  });

  describe('constructor', () => {
    it('should initialize with provided configuration', () => {
      expect(watcher).toBeDefined();
      expect(watcher.getStats().isWatching).toBe(false);
    });

    it('should apply default configuration values', () => {
      const minimalConfig: FileWatcherConfig = {
        translationDir: tempDir
      };
      const minimalWatcher = new TranslationFileWatcher(minimalConfig, index);
      
      expect(minimalWatcher).toBeDefined();
      minimalWatcher.removeAllListeners();
    });
  });

  describe('start and stop', () => {
    it('should start watching successfully', async () => {
      await watcher.start();
      
      expect(watcher.getStats().isWatching).toBe(true);
    });

    it('should throw error for non-existent directory', async () => {
      const invalidConfig: FileWatcherConfig = {
        translationDir: '/nonexistent/directory'
      };
      const invalidWatcher = new TranslationFileWatcher(invalidConfig, index);
      
      await expect(invalidWatcher.start()).rejects.toThrow(/Translation directory does not exist/);
      invalidWatcher.removeAllListeners();
    });

    it('should stop watching successfully', async () => {
      await watcher.start();
      expect(watcher.getStats().isWatching).toBe(true);
      
      await watcher.stop();
      expect(watcher.getStats().isWatching).toBe(false);
    });

    it('should emit ready event after initial scan', async () => {
      const readyHandler = vi.fn();
      watcher.on('ready', readyHandler);
      
      await watcher.start();
      
      // Wait a bit for the ready event
      await globalThis.testUtils.sleep(100);
      
      expect(readyHandler).toHaveBeenCalled();
    });
  });

  describe('file processing', () => {
    beforeEach(async () => {
      await watcher.start();
      await globalThis.testUtils.sleep(100); // Wait for initial scan
    });

    it('should process new translation files', async () => {
      const fileProcessedHandler = vi.fn();
      watcher.on('fileProcessed', fileProcessedHandler);

      const translationData = {
        common: {
          buttons: {
            submit: 'Submit',
            cancel: 'Cancel'
          }
        }
      };

      const filePath = join(tempDir, 'en.json');
      await fs.writeFile(filePath, JSON.stringify(translationData, null, 2));

      // Wait for file processing
      await globalThis.testUtils.sleep(200);

      expect(fileProcessedHandler).toHaveBeenCalledWith({
        type: 'add',
        path: filePath,
        language: 'en',
        timestamp: expect.any(Number)
      });

      // Check that translations were added to index
      expect(index.get('common.buttons.submit', 'en')?.value).toBe('Submit');
      expect(index.get('common.buttons.cancel', 'en')?.value).toBe('Cancel');
    });

    it('should process file changes', async () => {
      const fileProcessedHandler = vi.fn();
      
      // First create a file
      const translationData = {
        common: { button: 'Button' }
      };
      const filePath = join(tempDir, 'en.json');
      await fs.writeFile(filePath, JSON.stringify(translationData, null, 2));
      await globalThis.testUtils.sleep(200);
      
      // Now set up handler and modify the file
      watcher.on('fileProcessed', fileProcessedHandler);

      const updatedData = {
        common: { 
          button: 'Updated Button',
          newKey: 'New Value'
        }
      };
      await fs.writeFile(filePath, JSON.stringify(updatedData, null, 2));

      // Wait for file processing
      await globalThis.testUtils.sleep(200);

      expect(fileProcessedHandler).toHaveBeenCalledWith({
        type: 'change',
        path: filePath,
        language: 'en',
        timestamp: expect.any(Number)
      });

      // Check that translations were updated
      expect(index.get('common.button', 'en')?.value).toBe('Updated Button');
      expect(index.get('common.newKey', 'en')?.value).toBe('New Value');
    });

    it('should handle file deletion', async () => {
      const fileProcessedHandler = vi.fn();
      
      // First create and process a file
      const translationData = { test: { key: 'value' } };
      const filePath = join(tempDir, 'test.json');
      await fs.writeFile(filePath, JSON.stringify(translationData, null, 2));
      await globalThis.testUtils.sleep(200);
      
      // Verify translation was added
      expect(index.get('test.key', 'test')?.value).toBe('value');
      
      // Now set up handler and delete the file
      watcher.on('fileProcessed', fileProcessedHandler);
      await fs.unlink(filePath);

      // Wait for file processing
      await globalThis.testUtils.sleep(200);

      expect(fileProcessedHandler).toHaveBeenCalledWith({
        type: 'unlink',
        path: filePath,
        language: 'test',
        timestamp: expect.any(Number)
      });

      // Check that translation was removed
      expect(index.get('test.key', 'test')).toBeUndefined();
    });

    it('should handle nested translation structures', async () => {
      const translationData = {
        auth: {
          login: {
            form: {
              title: 'Login Form',
              fields: {
                username: 'Username',
                password: 'Password'
              }
            },
            buttons: {
              submit: 'Sign In',
              cancel: 'Cancel'
            }
          }
        }
      };

      const filePath = join(tempDir, 'en.json');
      await fs.writeFile(filePath, JSON.stringify(translationData, null, 2));
      await globalThis.testUtils.sleep(200);

      // Check deeply nested translations
      expect(index.get('auth.login.form.title', 'en')?.value).toBe('Login Form');
      expect(index.get('auth.login.form.fields.username', 'en')?.value).toBe('Username');
      expect(index.get('auth.login.form.fields.password', 'en')?.value).toBe('Password');
      expect(index.get('auth.login.buttons.submit', 'en')?.value).toBe('Sign In');
    });

    it('should handle malformed JSON gracefully', async () => {
      const errorHandler = vi.fn();
      watcher.on('error', errorHandler);

      const filePath = join(tempDir, 'malformed.json');
      await fs.writeFile(filePath, '{ invalid json content }');
      await globalThis.testUtils.sleep(200);

      expect(errorHandler).toHaveBeenCalled();
      const errorArg = errorHandler.mock.calls[0]?.[0];
      expect(errorArg.message).toContain('Failed to process file');
    });

    it('should extract language from filename correctly', async () => {
      const testCases = [
        { filename: 'en.json', expectedLang: 'en' },
        { filename: 'es-ES.json', expectedLang: 'es-ES' },
        { filename: 'zh-CN.json', expectedLang: 'zh-CN' },
        { filename: 'fr_FR.json', expectedLang: 'fr_FR' }
      ];

      for (const testCase of testCases) {
        const fileProcessedHandler = vi.fn();
        watcher.on('fileProcessed', fileProcessedHandler);

        const filePath = join(tempDir, testCase.filename);
        await fs.writeFile(filePath, JSON.stringify({ test: 'value' }));
        await globalThis.testUtils.sleep(200);

        expect(fileProcessedHandler).toHaveBeenCalledWith({
          type: 'add',
          path: filePath,
          language: testCase.expectedLang,
          timestamp: expect.any(Number)
        });

        watcher.removeAllListeners();
        await fs.unlink(filePath);
        await globalThis.testUtils.sleep(100);
      }
    });

    it('should ignore non-JSON files', async () => {
      const fileProcessedHandler = vi.fn();
      watcher.on('fileProcessed', fileProcessedHandler);

      // Create non-JSON files
      await fs.writeFile(join(tempDir, 'readme.txt'), 'This is not a translation file');
      await fs.writeFile(join(tempDir, 'config.yaml'), 'config: value');
      await fs.writeFile(join(tempDir, 'script.js'), 'console.log("hello");');

      await globalThis.testUtils.sleep(200);

      // Should not process any of these files
      expect(fileProcessedHandler).not.toHaveBeenCalled();
    });
  });

  describe('debouncing', () => {
    beforeEach(async () => {
      await watcher.start();
      await globalThis.testUtils.sleep(100);
    });

    it('should debounce rapid file changes', async () => {
      const fileProcessedHandler = vi.fn();
      watcher.on('fileProcessed', fileProcessedHandler);

      const filePath = join(tempDir, 'debounce-test.json');

      // Make rapid successive writes
      for (let i = 0; i < 5; i++) {
        await fs.writeFile(filePath, JSON.stringify({ counter: i }));
        await globalThis.testUtils.sleep(10); // Very short delay between writes
      }

      // Wait for debouncing to settle
      await globalThis.testUtils.sleep(200);

      // Should only process the file once (or very few times) due to debouncing
      expect(fileProcessedHandler.mock.calls.length).toBeLessThanOrEqual(2);
      
      // Final value should be the last write
      expect(index.get('counter', 'debounce-test')?.value).toBe(4);
    });
  });

  describe('manual file processing', () => {
    beforeEach(async () => {
      await watcher.start();
      await globalThis.testUtils.sleep(100);
    });

    it('should allow manual file processing', async () => {
      const translationData = { manual: { test: 'Manual Test' } };
      const filePath = join(tempDir, 'manual.json');
      await fs.writeFile(filePath, JSON.stringify(translationData, null, 2));

      // Process file manually instead of waiting for watcher
      await watcher.processFile(filePath);

      expect(index.get('manual.test', 'manual')?.value).toBe('Manual Test');
    });
  });

  describe('getWatchedFiles', () => {
    beforeEach(async () => {
      await watcher.start();
      await globalThis.testUtils.sleep(100);
    });

    it('should return list of watched JSON files', async () => {
      // Create some files
      await fs.writeFile(join(tempDir, 'en.json'), '{}');
      await fs.writeFile(join(tempDir, 'es.json'), '{}');
      await fs.writeFile(join(tempDir, 'readme.txt'), 'text'); // Should be ignored
      
      await globalThis.testUtils.sleep(200);

      const watchedFiles = watcher.getWatchedFiles();
      
      expect(watchedFiles.some(f => f.includes('en.json'))).toBe(true);
      expect(watchedFiles.some(f => f.includes('es.json'))).toBe(true);
      expect(watchedFiles.some(f => f.includes('readme.txt'))).toBe(false);
    });
  });

  describe('getStats', () => {
    it('should return watcher statistics', () => {
      const stats = watcher.getStats();

      expect(stats).toHaveProperty('isWatching');
      expect(stats).toHaveProperty('watchedFiles');
      expect(stats).toHaveProperty('processedFiles');
      
      expect(stats.isWatching).toBe(false); // Not started yet
      expect(stats.watchedFiles).toBeTypeOf('number');
      expect(stats.processedFiles).toBeTypeOf('number');
    });

    it('should update stats when watching', async () => {
      const beforeStats = watcher.getStats();
      expect(beforeStats.isWatching).toBe(false);

      await watcher.start();
      
      const afterStats = watcher.getStats();
      expect(afterStats.isWatching).toBe(true);
    });
  });

  describe('error handling', () => {
    beforeEach(async () => {
      await watcher.start();
      await globalThis.testUtils.sleep(100);
    });

    it('should emit error events for processing failures', async () => {
      const errorHandler = vi.fn();
      watcher.on('error', errorHandler);

      // Create a file with invalid JSON
      const filePath = join(tempDir, 'invalid.json');
      await fs.writeFile(filePath, 'not valid json {{{');
      await globalThis.testUtils.sleep(200);

      expect(errorHandler).toHaveBeenCalled();
    });

    it('should continue watching after processing errors', async () => {
      const fileProcessedHandler = vi.fn();
      watcher.on('fileProcessed', fileProcessedHandler);

      // Create invalid file
      const invalidPath = join(tempDir, 'invalid.json');
      await fs.writeFile(invalidPath, 'invalid json');
      await globalThis.testUtils.sleep(200);

      // Create valid file after error
      const validPath = join(tempDir, 'valid.json');
      await fs.writeFile(validPath, JSON.stringify({ test: 'valid' }));
      await globalThis.testUtils.sleep(200);

      // Should still process valid files after errors
      expect(fileProcessedHandler).toHaveBeenCalledWith({
        type: 'add',
        path: validPath,
        language: 'valid',
        timestamp: expect.any(Number)
      });
    });
  });

  describe('file clearing and updating', () => {
    beforeEach(async () => {
      await watcher.start();
      await globalThis.testUtils.sleep(100);
    });

    it('should clear previous translations when file is updated', async () => {
      const filePath = join(tempDir, 'update-test.json');

      // Initial file content
      const initialData = {
        old: { key: 'old value' },
        common: { key: 'common value' }
      };
      await fs.writeFile(filePath, JSON.stringify(initialData, null, 2));
      await globalThis.testUtils.sleep(200);

      // Verify initial content was loaded
      expect(index.get('old.key', 'update-test')?.value).toBe('old value');
      expect(index.get('common.key', 'update-test')?.value).toBe('common value');

      // Update file content (remove 'old' key, keep 'common', add 'new')
      const updatedData = {
        common: { key: 'updated common value' },
        new: { key: 'new value' }
      };
      await fs.writeFile(filePath, JSON.stringify(updatedData, null, 2));
      await globalThis.testUtils.sleep(200);

      // Old key should be removed, common should be updated, new should be added
      expect(index.get('old.key', 'update-test')).toBeUndefined();
      expect(index.get('common.key', 'update-test')?.value).toBe('updated common value');
      expect(index.get('new.key', 'update-test')?.value).toBe('new value');
    });
  });

  describe('ignored patterns', () => {
    it('should ignore files matching ignore patterns', async () => {
      const customConfig: FileWatcherConfig = {
        translationDir: tempDir,
        ignored: ['**/temp/**', '**/backup_*.json']
      };
      const customWatcher = new TranslationFileWatcher(customConfig, index);

      await customWatcher.start();
      await globalThis.testUtils.sleep(100);

      const fileProcessedHandler = vi.fn();
      customWatcher.on('fileProcessed', fileProcessedHandler);

      // Create temp directory and file (should be ignored)
      const tempSubDir = join(tempDir, 'temp');
      await fs.mkdir(tempSubDir);
      await fs.writeFile(join(tempSubDir, 'ignored.json'), '{"test": "ignored"}');

      // Create backup file (should be ignored)
      await fs.writeFile(join(tempDir, 'backup_en.json'), '{"test": "backup"}');

      // Create normal file (should be processed)
      await fs.writeFile(join(tempDir, 'normal.json'), '{"test": "normal"}');

      await globalThis.testUtils.sleep(300);

      // Only the normal file should be processed
      expect(fileProcessedHandler).toHaveBeenCalledTimes(1);
      expect(fileProcessedHandler).toHaveBeenCalledWith({
        type: 'add',
        path: join(tempDir, 'normal.json'),
        language: 'normal',
        timestamp: expect.any(Number)
      });

      await customWatcher.stop();
      customWatcher.removeAllListeners();
    });
  });
});
