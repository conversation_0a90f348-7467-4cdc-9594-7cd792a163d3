/**
 * Tests for TypeScript type generator
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { TypeGenerator, TypeGenerationUtils } from '../../src/core/type-generator.js';
import { TranslationIndex } from '../../src/core/translation-index.js';

describe('TypeGenerator', () => {
  let generator: TypeGenerator;
  let mockIndex: TranslationIndex;

  beforeEach(() => {
    mockIndex = new TranslationIndex({
      baseLanguage: 'en',
      cacheSize: 100
    });
    
    // Add test translations
    mockIndex.set('common.buttons.submit', 'en', 'Submit');
    mockIndex.set('common.buttons.cancel', 'en', 'Cancel');
    mockIndex.set('common.messages.success', 'en', 'Success!');
    mockIndex.set('auth.login.title', 'en', 'Login');
    mockIndex.set('auth.login.subtitle', 'en', 'Please enter your credentials');
    mockIndex.set('settings.theme', 'en', 'Theme');
    
    // Add other languages
    mockIndex.set('common.buttons.submit', 'es', 'Enviar');
    mockIndex.set('common.buttons.cancel', 'es', 'Cancelar');
    mockIndex.set('auth.login.title', 'fr', 'Connexion');

    generator = new TypeGenerator(mockIndex);
  });

  describe('generateTypes', () => {
    it('should generate basic TypeScript types', async () => {
      const mockWriteFile = vi.fn().mockResolvedValue(undefined);
      const mockMkdir = vi.fn().mockResolvedValue(undefined);

      vi.doMock('fs', () => ({
        promises: {
          writeFile: mockWriteFile,
          mkdir: mockMkdir
        }
      }));

      await generator.generateTypes({
        outputPath: './types/i18n.ts',
        namespace: 'I18n',
        strict: true,
        baseLanguage: 'en'
      });

      expect(mockMkdir).toHaveBeenCalled();
      expect(mockWriteFile).toHaveBeenCalled();

      const generatedContent = mockWriteFile.mock.calls[0][1];
      expect(generatedContent).toContain('export type TranslationKey =');
      expect(generatedContent).toContain("'common.buttons.submit'");
      expect(generatedContent).toContain("'auth.login.title'");
      expect(generatedContent).toContain('export namespace I18n');
    });

    it('should generate strict literal union types', async () => {
      const mockWriteFile = vi.fn().mockResolvedValue(undefined);
      const mockMkdir = vi.fn().mockResolvedValue(undefined);

      vi.doMock('fs', () => ({
        promises: {
          writeFile: mockWriteFile,
          mkdir: mockMkdir
        }
      }));

      await generator.generateTypes({
        outputPath: './types/i18n.ts',
        strict: true,
        baseLanguage: 'en'
      });

      const generatedContent = mockWriteFile.mock.calls[0][1];
      expect(generatedContent).toContain('export type TranslationKey =');
      expect(generatedContent).toContain("| 'common.buttons.submit'");
      expect(generatedContent).not.toContain('string & {');
    });

    it('should generate non-strict types with autocomplete hints', async () => {
      const mockWriteFile = vi.fn().mockResolvedValue(undefined);
      const mockMkdir = vi.fn().mockResolvedValue(undefined);

      vi.doMock('fs', () => ({
        promises: {
          writeFile: mockWriteFile,
          mkdir: mockMkdir
        }
      }));

      await generator.generateTypes({
        outputPath: './types/i18n.ts',
        strict: false,
        baseLanguage: 'en'
      });

      const generatedContent = mockWriteFile.mock.calls[0][1];
      expect(generatedContent).toContain('string & {');
      expect(generatedContent).toContain('// Autocomplete hints:');
    });

    it('should include value types when requested', async () => {
      const mockWriteFile = vi.fn().mockResolvedValue(undefined);
      const mockMkdir = vi.fn().mockResolvedValue(undefined);

      vi.doMock('fs', () => ({
        promises: {
          writeFile: mockWriteFile,
          mkdir: mockMkdir
        }
      }));

      await generator.generateTypes({
        outputPath: './types/i18n.ts',
        includeValues: true,
        baseLanguage: 'en'
      });

      const generatedContent = mockWriteFile.mock.calls[0][1];
      expect(generatedContent).toContain('export interface TranslationValues');
      expect(generatedContent).toContain("'common.buttons.submit': string;");
    });

    it('should generate namespace with correct language union', async () => {
      const mockWriteFile = vi.fn().mockResolvedValue(undefined);
      const mockMkdir = vi.fn().mockResolvedValue(undefined);

      vi.doMock('fs', () => ({
        promises: {
          writeFile: mockWriteFile,
          mkdir: mockMkdir
        }
      }));

      await generator.generateTypes({
        outputPath: './types/i18n.ts',
        namespace: 'MyI18n',
        baseLanguage: 'en'
      });

      const generatedContent = mockWriteFile.mock.calls[0][1];
      expect(generatedContent).toContain('export namespace MyI18n');
      expect(generatedContent).toContain("export type Language = 'en' | 'es' | 'fr';");
    });

    it('should include file header with timestamp', async () => {
      const mockWriteFile = vi.fn().mockResolvedValue(undefined);
      const mockMkdir = vi.fn().mockResolvedValue(undefined);

      vi.doMock('fs', () => ({
        promises: {
          writeFile: mockWriteFile,
          mkdir: mockMkdir
        }
      }));

      await generator.generateTypes({
        outputPath: './types/i18n.ts',
        baseLanguage: 'en'
      });

      const generatedContent = mockWriteFile.mock.calls[0][1];
      expect(generatedContent).toContain('Auto-generated translation types');
      expect(generatedContent).toContain('Generated at:');
      expect(generatedContent).toContain('Do not edit manually');
    });

    it('should handle directory creation errors', async () => {
      vi.doMock('fs', () => ({
        promises: {
          mkdir: vi.fn().mockRejectedValue(new Error('Permission denied'))
        }
      }));

      await expect(generator.generateTypes({
        outputPath: './types/i18n.ts',
        baseLanguage: 'en'
      })).rejects.toThrow('Failed to generate types');
    });
  });

  describe('value type inference', () => {
    beforeEach(() => {
      // Add different value types
      mockIndex.set('config.maxItems', 'en', 42);
      mockIndex.set('config.enabled', 'en', true);
      mockIndex.set('config.tags', 'en', ['tag1', 'tag2']);
      mockIndex.set('config.metadata', 'en', { version: '1.0' });
    });

    it('should infer correct types for different values', async () => {
      const mockWriteFile = vi.fn().mockResolvedValue(undefined);
      const mockMkdir = vi.fn().mockResolvedValue(undefined);

      vi.doMock('fs', () => ({
        promises: {
          writeFile: mockWriteFile,
          mkdir: mockMkdir
        }
      }));

      await generator.generateTypes({
        outputPath: './types/i18n.ts',
        includeValues: true,
        baseLanguage: 'en'
      });

      const generatedContent = mockWriteFile.mock.calls[0][1];
      expect(generatedContent).toContain("'config.maxItems': number;");
      expect(generatedContent).toContain("'config.enabled': boolean;");
      expect(generatedContent).toContain("'config.tags': string[];");
      expect(generatedContent).toContain("'config.metadata': Record<string, any>;");
    });
  });

  describe('watchAndRegenerate', () => {
    it('should set up event listeners for regeneration', async () => {
      const mockOn = vi.fn();
      const mockGenerateTypes = vi.spyOn(generator, 'generateTypes').mockResolvedValue(undefined);

      // Mock the index to have an 'on' method
      (mockIndex as any).on = mockOn;

      await generator.watchAndRegenerate({
        outputPath: './types/i18n.ts',
        baseLanguage: 'en'
      });

      expect(mockOn).toHaveBeenCalledWith('set', expect.any(Function));
      expect(mockOn).toHaveBeenCalledWith('delete', expect.any(Function));

      // Simulate a 'set' event
      const setCallback = mockOn.mock.calls.find(call => call[0] === 'set')[1];
      await setCallback();

      expect(mockGenerateTypes).toHaveBeenCalled();
    });
  });
});

describe('TypeGenerationUtils', () => {
  describe('validateTypes', () => {
    it('should validate correct TypeScript types', async () => {
      const validTypes = `
        export type TranslationKey = 'common.submit' | 'common.cancel';
        export namespace I18n {
          export type Language = 'en' | 'es';
        }
      `;

      vi.doMock('fs', () => ({
        promises: {
          readFile: vi.fn().mockResolvedValue(validTypes)
        }
      }));

      const result = await TypeGenerationUtils.validateTypes('./types/i18n.ts');

      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect missing TranslationKey export', async () => {
      const invalidTypes = `
        export namespace I18n {
          export type Language = 'en' | 'es';
        }
      `;

      vi.doMock('fs', () => ({
        promises: {
          readFile: vi.fn().mockResolvedValue(invalidTypes)
        }
      }));

      const result = await TypeGenerationUtils.validateTypes('./types/i18n.ts');

      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Missing TranslationKey type export');
    });

    it('should detect unbalanced braces', async () => {
      const invalidTypes = `
        export type TranslationKey = 'common.submit';
        export namespace I18n {
          export type Language = 'en';
        // Missing closing brace
      `;

      vi.doMock('fs', () => ({
        promises: {
          readFile: vi.fn().mockResolvedValue(invalidTypes)
        }
      }));

      const result = await TypeGenerationUtils.validateTypes('./types/i18n.ts');

      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Unbalanced braces in generated types');
    });

    it('should handle file read errors', async () => {
      vi.doMock('fs', () => ({
        promises: {
          readFile: vi.fn().mockRejectedValue(new Error('File not found'))
        }
      }));

      const result = await TypeGenerationUtils.validateTypes('./nonexistent.ts');

      expect(result.valid).toBe(false);
      expect(result.errors[0]).toContain('Failed to validate types');
    });
  });

  describe('generateKeyDocumentation', () => {
    let mockIndex: TranslationIndex;

    beforeEach(() => {
      mockIndex = new TranslationIndex({
        baseLanguage: 'en',
        cacheSize: 100
      });
      
      mockIndex.set('common.submit', 'en', 'Submit');
      mockIndex.set('common.cancel', 'en', 'Cancel');
      mockIndex.set('auth.login', 'en', 'Login to your account');
    });

    it('should generate JSDoc comments for translation keys', () => {
      const keys = ['common.submit', 'common.cancel', 'auth.login'];
      const docs = TypeGenerationUtils.generateKeyDocumentation(keys, mockIndex, 'en');

      expect(docs['common.submit']).toBe('/** Submit */');
      expect(docs['common.cancel']).toBe('/** Cancel */');
      expect(docs['auth.login']).toBe('/** Login to your account */');
    });

    it('should handle missing translations', () => {
      const keys = ['common.submit', 'missing.key'];
      const docs = TypeGenerationUtils.generateKeyDocumentation(keys, mockIndex, 'en');

      expect(docs['common.submit']).toBe('/** Submit */');
      expect(docs['missing.key']).toBe('/** Translation key: missing.key */');
    });

    it('should handle long values', () => {
      const longText = 'This is a very long translation text that exceeds the maximum length for JSDoc comments and should be truncated';
      mockIndex.set('long.text', 'en', longText);

      const keys = ['long.text'];
      const docs = TypeGenerationUtils.generateKeyDocumentation(keys, mockIndex, 'en');

      expect(docs['long.text']).toBe('/** Translation key: long.text */');
    });
  });
});
