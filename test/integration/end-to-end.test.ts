/**
 * Integration tests for the complete i18n MCP server system
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { TranslationMCPServer } from '../../src/server/mcp-server.js';
import { ServerConfig } from '../../src/types/translation.js';
import { promises as fs } from 'fs';
import { join } from 'path';

// Mock MCP SDK for integration tests
vi.mock('@modelcontextprotocol/sdk/server/mcp.js', () => {
  return {
    McpServer: vi.fn().mockImplementation(() => ({
      tool: vi.fn(),
      connect: vi.fn().mockResolvedValue(undefined)
    }))
  };
});

vi.mock('@modelcontextprotocol/sdk/server/stdio.js', () => {
  return {
    StdioServerTransport: vi.fn().mockImplementation(() => ({}))
  };
});

describe('Integration Tests', () => {
  let tempDir: string;
  let server: TranslationMCPServer;
  let config: ServerConfig;

  beforeEach(async () => {
    tempDir = await globalThis.testUtils.createTempDir();
    
    config = {
      name: 'integration-test-server',
      version: '1.0.0',
      translationDir: tempDir,
      baseLanguage: 'en',
      debug: false,
      watchOptions: {
        debounceMs: 50,
        ignored: ['**/node_modules/**']
      }
    };
  });

  afterEach(async () => {
    if (server) {
      try {
        await server.stop();
      } catch (error) {
        // Ignore stop errors
      }
      server.getIndex().removeAllListeners();
      server.getFileWatcher().removeAllListeners();
    }
    await globalThis.testUtils.cleanupTempDir(tempDir);
  });

  describe('End-to-End Translation Management', () => {
    it('should handle complete translation workflow', async () => {
      // Create initial translation files
      const enTranslations = {
        common: {
          buttons: {
            submit: 'Submit',
            cancel: 'Cancel'
          },
          messages: {
            success: 'Success!',
            error: 'Error occurred'
          }
        },
        auth: {
          login: {
            title: 'Login',
            subtitle: 'Please sign in'
          }
        }
      };

      const esTranslations = {
        common: {
          buttons: {
            submit: 'Enviar',
            cancel: 'Cancelar'
          },
          messages: {
            success: '¡Éxito!',
            error: 'Ocurrió un error'
          }
        },
        auth: {
          login: {
            title: 'Iniciar sesión'
            // Missing subtitle for testing
          }
        }
      };

      await fs.writeFile(join(tempDir, 'en.json'), JSON.stringify(enTranslations, null, 2));
      await fs.writeFile(join(tempDir, 'es.json'), JSON.stringify(esTranslations, null, 2));

      // Initialize server
      server = new TranslationMCPServer(config);
      
      // Mock the file watcher to avoid actual file watching in tests
      const fileWatcher = server.getFileWatcher();
      vi.spyOn(fileWatcher, 'start').mockResolvedValue();
      vi.spyOn(fileWatcher, 'stop').mockResolvedValue();

      await server.start();

      // Manually process the files since we mocked the watcher
      await fileWatcher.processFile(join(tempDir, 'en.json'));
      await fileWatcher.processFile(join(tempDir, 'es.json'));

      const index = server.getIndex();

      // Test 1: Verify translations were loaded correctly
      expect(index.get('common.buttons.submit', 'en')?.value).toBe('Submit');
      expect(index.get('common.buttons.submit', 'es')?.value).toBe('Enviar');
      expect(index.get('auth.login.title', 'en')?.value).toBe('Login');
      expect(index.get('auth.login.title', 'es')?.value).toBe('Iniciar sesión');

      // Test 2: Search functionality works
      const searchResults = await index.search('submit', { scope: 'both' });
      expect(searchResults.length).toBeGreaterThan(0);
      expect(searchResults.some(r => r.keyPath === 'common.buttons.submit')).toBe(true);

      // Test 3: Context retrieval works
      const context = await index.getContext('common.buttons.submit', { depth: 1 });
      expect(context?.keyPath).toBe('common.buttons.submit');
      expect(context?.siblings.some(s => s.keyPath === 'common.buttons.cancel')).toBe(true);

      // Test 4: Structure validation detects missing translations
      const validation = await index.validateStructure();
      expect(validation.valid).toBe(false);
      expect(validation.missingKeys.es).toContain('auth.login.subtitle');

      // Test 5: Batch updates work
      const batchResult = await index.batchUpdate([
        { type: 'set', keyPath: 'auth.login.subtitle', language: 'es', value: 'Por favor inicia sesión' },
        { type: 'set', keyPath: 'common.buttons.save', language: 'en', value: 'Save' },
        { type: 'set', keyPath: 'common.buttons.save', language: 'es', value: 'Guardar' }
      ]);

      expect(batchResult.success).toBe(true);
      expect(index.get('auth.login.subtitle', 'es')?.value).toBe('Por favor inicia sesión');
      expect(index.get('common.buttons.save', 'en')?.value).toBe('Save');

      // Test 6: Structure validation now passes
      const secondValidation = await index.validateStructure();
      expect(secondValidation.valid).toBe(true);

      // Test 7: Usage analysis works
      const analysis = await index.analyzeUsage();
      expect(analysis.languageStats.en.completeness).toBe(1);
      expect(analysis.languageStats.es.completeness).toBe(1);

      // Test 8: Statistics are correct
      const stats = index.getStats();
      expect(stats.totalKeys).toBeGreaterThan(0);
      expect(stats.languages).toContain('en');
      expect(stats.languages).toContain('es');
    });

    it('should handle file changes and updates in real-time', async () => {
      server = new TranslationMCPServer(config);
      
      const fileWatcher = server.getFileWatcher();
      vi.spyOn(fileWatcher, 'start').mockResolvedValue();
      vi.spyOn(fileWatcher, 'stop').mockResolvedValue();

      await server.start();

      const index = server.getIndex();

      // Create initial file
      const initialTranslations = {
        test: {
          key1: 'Initial Value 1',
          key2: 'Initial Value 2'
        }
      };

      const filePath = join(tempDir, 'en.json');
      await fs.writeFile(filePath, JSON.stringify(initialTranslations, null, 2));
      await fileWatcher.processFile(filePath);

      // Verify initial state
      expect(index.get('test.key1', 'en')?.value).toBe('Initial Value 1');
      expect(index.get('test.key2', 'en')?.value).toBe('Initial Value 2');

      // Update file (modify one key, add one key, remove one key)
      const updatedTranslations = {
        test: {
          key1: 'Updated Value 1', // Modified
          key3: 'New Value 3'      // Added
          // key2 removed
        }
      };

      await fs.writeFile(filePath, JSON.stringify(updatedTranslations, null, 2));
      await fileWatcher.processFile(filePath);

      // Verify changes were applied
      expect(index.get('test.key1', 'en')?.value).toBe('Updated Value 1');
      expect(index.get('test.key3', 'en')?.value).toBe('New Value 3');
      expect(index.get('test.key2', 'en')).toBeUndefined(); // Should be removed
    });

    it('should handle multiple language files consistently', async () => {
      server = new TranslationMCPServer(config);
      
      const fileWatcher = server.getFileWatcher();
      vi.spyOn(fileWatcher, 'start').mockResolvedValue();
      vi.spyOn(fileWatcher, 'stop').mockResolvedValue();

      await server.start();

      const translations = {
        app: {
          name: 'My App',
          version: '1.0.0'
        },
        features: {
          auth: 'Authentication',
          dashboard: 'Dashboard'
        }
      };

      const languages = [
        { code: 'en', translations: translations },
        { 
          code: 'es', 
          translations: {
            app: {
              name: 'Mi Aplicación',
              version: '1.0.0'
            },
            features: {
              auth: 'Autenticación',
              dashboard: 'Tablero'
            }
          }
        },
        {
          code: 'fr',
          translations: {
            app: {
              name: 'Mon App',
              version: '1.0.0'
            },
            features: {
              auth: 'Authentification',
              dashboard: 'Tableau de bord'
            }
          }
        }
      ];

      // Create and process all language files
      for (const lang of languages) {
        const filePath = join(tempDir, `${lang.code}.json`);
        await fs.writeFile(filePath, JSON.stringify(lang.translations, null, 2));
        await fileWatcher.processFile(filePath);
      }

      const index = server.getIndex();

      // Test cross-language consistency
      for (const lang of languages) {
        expect(index.get('app.name', lang.code)?.value).toBeDefined();
        expect(index.get('features.auth', lang.code)?.value).toBeDefined();
        expect(index.get('features.dashboard', lang.code)?.value).toBeDefined();
      }

      // Test language statistics
      const stats = index.getStats();
      expect(stats.languages).toHaveLength(3);
      expect(stats.languages).toEqual(expect.arrayContaining(['en', 'es', 'fr']));

      // Test search across languages
      const searchResults = await index.search('app', { scope: 'keys' });
      expect(searchResults.length).toBeGreaterThan(0);
      
      for (const result of searchResults) {
        if (result.keyPath.startsWith('app.')) {
          expect(Object.keys(result.translations)).toHaveLength(3);
        }
      }
    });

    it('should handle error scenarios gracefully', async () => {
      server = new TranslationMCPServer(config);
      
      const fileWatcher = server.getFileWatcher();
      vi.spyOn(fileWatcher, 'start').mockResolvedValue();
      vi.spyOn(fileWatcher, 'stop').mockResolvedValue();

      await server.start();

      const index = server.getIndex();

      // Test invalid batch operations
      const invalidBatchResult = await index.batchUpdate([
        { type: 'set', keyPath: 'valid.key', language: 'en', value: 'Valid' },
        { type: 'set', keyPath: 'invalid..key', language: 'en', value: 'Invalid' }, // Invalid key
        { type: 'set', keyPath: 'another.key', language: 'en', value: 'Another' }
      ]);

      expect(invalidBatchResult.success).toBe(false);
      expect(invalidBatchResult.errors.length).toBeGreaterThan(0);

      // Ensure rollback worked - no translations should exist
      expect(index.get('valid.key', 'en')).toBeUndefined();
      expect(index.get('another.key', 'en')).toBeUndefined();

      // Test malformed JSON file handling
      const errorHandler = vi.fn();
      fileWatcher.on('error', errorHandler);

      const malformedPath = join(tempDir, 'malformed.json');
      await fs.writeFile(malformedPath, '{ invalid json content }');
      
      try {
        await fileWatcher.processFile(malformedPath);
      } catch (error) {
        // Expected to fail
      }

      expect(errorHandler).toHaveBeenCalled();
    });
  });

  describe('Performance and Scalability', () => {
    it('should handle large translation datasets efficiently', async () => {
      server = new TranslationMCPServer(config);
      
      const fileWatcher = server.getFileWatcher();
      vi.spyOn(fileWatcher, 'start').mockResolvedValue();
      vi.spyOn(fileWatcher, 'stop').mockResolvedValue();

      await server.start();

      const index = server.getIndex();

      // Generate large dataset
      const startTime = Date.now();
      
      for (let category = 0; category < 50; category++) {
        for (let item = 0; item < 100; item++) {
          index.set(`category${category}.item${item}.name`, 'en', `Item ${category}-${item}`);
          index.set(`category${category}.item${item}.description`, 'en', `Description for item ${category}-${item}`);
        }
      }

      const loadTime = Date.now() - startTime;
      expect(loadTime).toBeLessThan(2000); // Should load 10k translations in under 2 seconds

      // Test search performance
      const searchStart = Date.now();
      const searchResults = await index.search('item', { scope: 'keys', maxResults: 100 });
      const searchTime = Date.now() - searchStart;

      expect(searchTime).toBeLessThan(100); // Search should be under 100ms
      expect(searchResults).toHaveLength(100); // Should respect maxResults

      // Test prefix search performance
      const prefixStart = Date.now();
      const prefixResults = index.searchByPrefix('category1.');
      const prefixTime = Date.now() - prefixStart;

      expect(prefixTime).toBeLessThan(50); // Prefix search should be very fast
      expect(prefixResults.length).toBeGreaterThan(0);

      // Test statistics
      const stats = index.getStats();
      expect(stats.totalKeys).toBe(10000); // 50 categories * 100 items * 2 fields
      expect(stats.totalTranslations).toBe(10000);
    });

    it('should maintain cache efficiency under load', async () => {
      server = new TranslationMCPServer(config);
      
      const fileWatcher = server.getFileWatcher();
      vi.spyOn(fileWatcher, 'start').mockResolvedValue();
      vi.spyOn(fileWatcher, 'stop').mockResolvedValue();

      await server.start();

      const index = server.getIndex();

      // Add more items than cache size to test LRU behavior
      for (let i = 0; i < 1500; i++) {
        index.set(`cache.test${i}`, 'en', `Value ${i}`);
      }

      // Access some items multiple times to test caching
      const accessStart = Date.now();
      for (let i = 0; i < 100; i++) {
        for (let j = 0; j < 10; j++) {
          index.get(`cache.test${j}`, 'en');
        }
      }
      const accessTime = Date.now() - accessStart;

      expect(accessTime).toBeLessThan(100); // Cached access should be very fast

      const stats = index.getStats();
      expect(stats.cacheSize).toBeLessThanOrEqual(1000); // Should respect cache limits
    });
  });

  describe('Real-world Scenarios', () => {
    it('should handle typical web application translation structure', async () => {
      server = new TranslationMCPServer(config);
      
      const fileWatcher = server.getFileWatcher();
      vi.spyOn(fileWatcher, 'start').mockResolvedValue();
      vi.spyOn(fileWatcher, 'stop').mockResolvedValue();

      await server.start();

      // Realistic web app translation structure
      const webAppTranslations = {
        meta: {
          title: 'My Web App',
          description: 'A modern web application'
        },
        navigation: {
          home: 'Home',
          products: 'Products',
          about: 'About',
          contact: 'Contact',
          login: 'Login',
          signup: 'Sign Up'
        },
        common: {
          buttons: {
            submit: 'Submit',
            cancel: 'Cancel',
            save: 'Save',
            delete: 'Delete',
            edit: 'Edit',
            view: 'View',
            download: 'Download',
            upload: 'Upload'
          },
          forms: {
            required: 'Required field',
            optional: 'Optional',
            placeholder: {
              email: 'Enter your email',
              password: 'Enter your password',
              search: 'Search...'
            }
          },
          messages: {
            success: 'Operation completed successfully',
            error: 'An error occurred',
            warning: 'Warning: Please check your input',
            info: 'Information',
            loading: 'Loading...',
            noData: 'No data available',
            confirmDelete: 'Are you sure you want to delete this item?'
          }
        },
        auth: {
          login: {
            title: 'Sign In',
            subtitle: 'Welcome back! Please sign in to your account.',
            form: {
              email: 'Email Address',
              password: 'Password',
              remember: 'Remember me',
              forgotPassword: 'Forgot your password?'
            },
            actions: {
              signIn: 'Sign In',
              signUp: 'Create Account',
              resetPassword: 'Reset Password'
            }
          },
          register: {
            title: 'Create Account',
            subtitle: 'Join us today! Create your new account.',
            form: {
              firstName: 'First Name',
              lastName: 'Last Name',
              email: 'Email Address',
              password: 'Password',
              confirmPassword: 'Confirm Password',
              acceptTerms: 'I accept the terms and conditions'
            }
          }
        },
        dashboard: {
          welcome: 'Welcome to your dashboard',
          stats: {
            users: 'Total Users',
            orders: 'Total Orders',
            revenue: 'Total Revenue',
            growth: 'Growth Rate'
          },
          charts: {
            salesOverTime: 'Sales Over Time',
            topProducts: 'Top Products',
            userActivity: 'User Activity'
          }
        },
        products: {
          list: {
            title: 'Products',
            search: 'Search products...',
            filters: {
              category: 'Category',
              priceRange: 'Price Range',
              availability: 'Availability'
            },
            sort: {
              name: 'Name',
              price: 'Price',
              popularity: 'Popularity',
              newest: 'Newest'
            }
          },
          detail: {
            description: 'Description',
            specifications: 'Specifications',
            reviews: 'Reviews',
            relatedProducts: 'Related Products',
            addToCart: 'Add to Cart',
            buyNow: 'Buy Now'
          }
        }
      };

      const filePath = join(tempDir, 'en.json');
      await fs.writeFile(filePath, JSON.stringify(webAppTranslations, null, 2));
      await fileWatcher.processFile(filePath);

      const index = server.getIndex();

      // Test deep path access
      expect(index.get('auth.login.form.email', 'en')?.value).toBe('Email Address');
      expect(index.get('products.detail.addToCart', 'en')?.value).toBe('Add to Cart');
      expect(index.get('common.forms.placeholder.search', 'en')?.value).toBe('Search...');

      // Test context retrieval for nested structures
      const authContext = await index.getContext('auth.login.form.email', { depth: 2 });
      expect(authContext?.parent?.keyPath).toBe('auth.login.form');
      expect(authContext?.siblings.some(s => s.keyPath === 'auth.login.form.password')).toBe(true);

      // Test searching for common terms
      const buttonSearch = await index.search('button', { scope: 'keys' });
      expect(buttonSearch.some(r => r.keyPath.includes('buttons'))).toBe(true);

      const submitSearch = await index.search('submit', { scope: 'values' });
      expect(submitSearch.some(r => r.translations.en?.value === 'Submit')).toBe(true);

      // Test prefix searches for different sections
      const authPaths = index.searchByPrefix('auth.');
      expect(authPaths.length).toBeGreaterThan(10);

      const commonPaths = index.searchByPrefix('common.buttons.');
      expect(commonPaths.length).toBe(8); // 8 buttons defined

      // Test statistics for realistic app
      const stats = index.getStats();
      expect(stats.totalKeys).toBeGreaterThan(50);
      expect(stats.languages).toEqual(['en']);
    });

    it('should handle gradual translation completion workflow', async () => {
      server = new TranslationMCPServer(config);
      
      const fileWatcher = server.getFileWatcher();
      vi.spyOn(fileWatcher, 'start').mockResolvedValue();
      vi.spyOn(fileWatcher, 'stop').mockResolvedValue();

      await server.start();

      const index = server.getIndex();

      // Step 1: Start with English base
      const baseTranslations = {
        app: { name: 'App Name' },
        common: {
          save: 'Save',
          cancel: 'Cancel',
          delete: 'Delete'
        },
        pages: {
          home: 'Home',
          about: 'About'
        }
      };

      await fs.writeFile(join(tempDir, 'en.json'), JSON.stringify(baseTranslations, null, 2));
      await fileWatcher.processFile(join(tempDir, 'en.json'));

      // Step 2: Add partial Spanish translation
      const partialSpanish = {
        app: { name: 'Nombre de la App' },
        common: {
          save: 'Guardar',
          cancel: 'Cancelar'
          // delete missing
        }
        // pages missing entirely
      };

      await fs.writeFile(join(tempDir, 'es.json'), JSON.stringify(partialSpanish, null, 2));
      await fileWatcher.processFile(join(tempDir, 'es.json'));

      // Step 3: Validate and check completion
      let validation = await index.validateStructure();
      expect(validation.valid).toBe(false);
      expect(validation.missingKeys.es).toContain('common.delete');
      expect(validation.missingKeys.es).toContain('pages.home');
      expect(validation.missingKeys.es).toContain('pages.about');

      let analysis = await index.analyzeUsage();
      expect(analysis.languageStats.en.completeness).toBe(1);
      expect(analysis.languageStats.es.completeness).toBeLessThan(1);

      // Step 4: Complete Spanish translation using batch update
      const completionOperations = [
        { type: 'set' as const, keyPath: 'common.delete', language: 'es', value: 'Eliminar' },
        { type: 'set' as const, keyPath: 'pages.home', language: 'es', value: 'Inicio' },
        { type: 'set' as const, keyPath: 'pages.about', language: 'es', value: 'Acerca de' }
      ];

      const batchResult = await index.batchUpdate(completionOperations);
      expect(batchResult.success).toBe(true);

      // Step 5: Verify completion
      validation = await index.validateStructure();
      expect(validation.valid).toBe(true);

      analysis = await index.analyzeUsage();
      expect(analysis.languageStats.es.completeness).toBe(1);

      // Step 6: Add third language with auto-fix
      const partialFrench = {
        app: { name: 'Nom de l\'App' },
        common: {
          save: 'Sauvegarder'
          // Most translations missing
        }
      };

      await fs.writeFile(join(tempDir, 'fr.json'), JSON.stringify(partialFrench, null, 2));
      await fileWatcher.processFile(join(tempDir, 'fr.json'));

      // Use auto-fix to add missing translations
      const autoFixValidation = await index.validateStructure({ autoFix: true });
      expect(autoFixValidation.structuralIssues.some(issue => issue.includes('Auto-fixed'))).toBe(true);

      // Verify auto-fix added placeholder translations
      expect(index.get('common.cancel', 'fr')?.value).toContain('[MISSING:');
      expect(index.get('pages.home', 'fr')?.value).toContain('[MISSING:');
    });
  });
});
