/**
 * Performance and benchmark tests for the i18n MCP server
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { TranslationIndex } from '../../src/core/translation-index.js';
import { TranslationFileWatcher } from '../../src/core/file-watcher.js';
import { PathParser } from '../../src/utils/path-parser.js';
import { JsonOperations } from '../../src/utils/json-operations.js';
import { promises as fs } from 'fs';
import { join } from 'path';

describe('Performance Tests', () => {
  let tempDir: string;

  beforeEach(async () => {
    tempDir = await globalThis.testUtils.createTempDir();
  });

  afterEach(async () => {
    await globalThis.testUtils.cleanupTempDir(tempDir);
  });

  describe('TranslationIndex Performance', () => {
    let index: TranslationIndex;

    beforeEach(() => {
      index = new TranslationIndex({ 
        baseLanguage: 'en',
        maxCacheSize: 10000 
      });
    });

    afterEach(() => {
      index.removeAllListeners();
    });

    it('should handle large datasets efficiently', () => {
      const startTime = performance.now();
      
      // Insert 10,000 translations across 5 languages
      for (let category = 0; category < 20; category++) {
        for (let item = 0; item < 100; item++) {
          const keyPath = `category${category}.item${item}.name`;
          index.set(keyPath, 'en', `Item ${category}-${item}`);
          index.set(keyPath, 'es', `Artículo ${category}-${item}`);
          index.set(keyPath, 'fr', `Article ${category}-${item}`);
          index.set(keyPath, 'de', `Artikel ${category}-${item}`);
          index.set(keyPath, 'it', `Articolo ${category}-${item}`);
        }
      }

      const insertTime = performance.now() - startTime;
      
      expect(insertTime).toBeLessThan(2000); // Should complete in under 2 seconds
      expect(index.getStats().totalKeys).toBe(2000);
      expect(index.getStats().totalTranslations).toBe(10000);
    });

    it('should perform searches efficiently on large datasets', async () => {
      // Setup large dataset
      for (let i = 0; i < 5000; i++) {
        index.set(`test.item${i}`, 'en', `Test Item ${i}`);
        index.set(`search.result${i}`, 'en', `Search Result ${i}`);
      }

      const searchStartTime = performance.now();
      const results = await index.search('test', { scope: 'keys', maxResults: 100 });
      const searchTime = performance.now() - searchStartTime;

      expect(searchTime).toBeLessThan(100); // Search should be under 100ms
      expect(results).toHaveLength(100);
    });

    it('should perform prefix searches efficiently', () => {
      // Setup hierarchical data
      for (let category = 0; category < 50; category++) {
        for (let item = 0; item < 50; item++) {
          index.set(`category${category}.item${item}`, 'en', `Value ${category}-${item}`);
        }
      }

      const prefixStartTime = performance.now();
      const results = index.searchByPrefix('category1.');
      const prefixTime = performance.now() - prefixStartTime;

      expect(prefixTime).toBeLessThan(50); // Prefix search should be very fast
      expect(results).toHaveLength(50); // Should find all items in category1
    });

    it('should handle concurrent read operations efficiently', async () => {
      // Setup data
      for (let i = 0; i < 1000; i++) {
        index.set(`concurrent.test${i}`, 'en', `Concurrent Test ${i}`);
      }

      const concurrentStartTime = performance.now();
      
      // Perform 1000 concurrent read operations
      const promises = [];
      for (let i = 0; i < 1000; i++) {
        promises.push(
          Promise.resolve(index.get(`concurrent.test${i % 100}`, 'en'))
        );
      }

      await Promise.all(promises);
      const concurrentTime = performance.now() - concurrentStartTime;

      expect(concurrentTime).toBeLessThan(500); // Should handle concurrent reads quickly
    });

    it('should maintain cache efficiency under pressure', () => {
      // Add more items than cache size to test LRU behavior
      const cacheSize = 1000;
      const itemCount = 2000;

      const startTime = performance.now();

      for (let i = 0; i < itemCount; i++) {
        index.set(`cache.test${i}`, 'en', `Cache Test ${i}`);
      }

      // Access items to test cache behavior
      for (let i = 0; i < 100; i++) {
        for (let j = 0; j < 10; j++) {
          index.get(`cache.test${j}`, 'en'); // These should be cached
        }
      }

      const totalTime = performance.now() - startTime;
      const stats = index.getStats();

      expect(totalTime).toBeLessThan(1000);
      expect(stats.cacheSize).toBeLessThanOrEqual(cacheSize);
    });

    it('should handle batch operations efficiently', async () => {
      const operations = [];
      
      // Create 1000 batch operations
      for (let i = 0; i < 1000; i++) {
        operations.push({
          type: 'set' as const,
          keyPath: `batch.item${i}`,
          language: 'en',
          value: `Batch Item ${i}`
        });
      }

      const batchStartTime = performance.now();
      const result = await index.batchUpdate(operations);
      const batchTime = performance.now() - batchStartTime;

      expect(result.success).toBe(true);
      expect(batchTime).toBeLessThan(500); // Batch operations should be fast
      expect(index.getStats().totalKeys).toBe(1000);
    });

    it('should perform structure validation efficiently', async () => {
      // Create a large, complex structure
      const languages = ['en', 'es', 'fr', 'de', 'it'];
      
      for (let category = 0; category < 10; category++) {
        for (let section = 0; section < 10; section++) {
          for (let item = 0; item < 10; item++) {
            const keyPath = `category${category}.section${section}.item${item}`;
            for (const lang of languages) {
              index.set(keyPath, lang, `Value ${category}-${section}-${item} in ${lang}`);
            }
          }
        }
      }

      // Add some missing translations to test validation
      index.delete('category0.section0.item0', 'es');
      index.delete('category1.section1.item1', 'fr');

      const validationStartTime = performance.now();
      const validation = await index.validateStructure();
      const validationTime = performance.now() - validationStartTime;

      expect(validationTime).toBeLessThan(1000); // Validation should complete quickly
      expect(validation.valid).toBe(false);
      expect(validation.missingKeys.es).toContain('category0.section0.item0');
      expect(validation.missingKeys.fr).toContain('category1.section1.item1');
    });

    it('should handle memory efficiently with large datasets', () => {
      const initialMemory = process.memoryUsage().heapUsed;
      
      // Add a large number of translations
      for (let i = 0; i < 10000; i++) {
        index.set(`memory.test${i}`, 'en', `Memory Test ${i}`.repeat(10)); // Larger strings
      }

      const afterAddMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = afterAddMemory - initialMemory;
      const bytesPerTranslation = memoryIncrease / 10000;

      // Should use reasonable memory per translation (rough estimate)
      expect(bytesPerTranslation).toBeLessThan(1000); // Less than 1KB per translation

      // Clear and check memory is freed
      index.clear();
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }

      const afterClearMemory = process.memoryUsage().heapUsed;
      const memoryFreed = afterAddMemory - afterClearMemory;
      
      // Should free a significant portion of memory
      expect(memoryFreed).toBeGreaterThan(memoryIncrease * 0.5);
    });
  });

  describe('PathParser Performance', () => {
    beforeEach(() => {
      PathParser.clearCache();
    });

    afterEach(() => {
      PathParser.clearCache();
    });

    it('should parse paths efficiently', () => {
      const paths = [
        'simple',
        'common.buttons.submit',
        'auth.login.form.fields.email',
        'dashboard.widgets.charts.sales.monthly.data',
        'very.deeply.nested.structure.with.many.levels.of.nesting.and.long.names'
      ];

      const startTime = performance.now();
      
      // Parse each path 1000 times
      for (let i = 0; i < 1000; i++) {
        for (const path of paths) {
          PathParser.parse(path);
        }
      }

      const parseTime = performance.now() - startTime;
      
      expect(parseTime).toBeLessThan(100); // Should parse 5000 paths in under 100ms
    });

    it('should benefit from caching', () => {
      const path = 'common.buttons.submit.confirmation.dialog.title';
      
      // First parse (no cache)
      const firstStartTime = performance.now();
      for (let i = 0; i < 1000; i++) {
        PathParser.parse(path);
      }
      const firstParseTime = performance.now() - firstStartTime;

      // Second parse (with cache)
      const secondStartTime = performance.now();
      for (let i = 0; i < 1000; i++) {
        PathParser.parse(path);
      }
      const secondParseTime = performance.now() - secondStartTime;

      // Cached parsing should be significantly faster
      expect(secondParseTime).toBeLessThan(firstParseTime * 0.1);
    });

    it('should handle large numbers of unique paths', () => {
      const startTime = performance.now();
      
      // Generate and parse many unique paths
      for (let i = 0; i < 10000; i++) {
        const path = `category${i % 100}.item${i}.property${i % 10}`;
        PathParser.parse(path);
      }

      const parseTime = performance.now() - startTime;
      
      expect(parseTime).toBeLessThan(500); // Should handle 10k unique paths quickly
      
      const stats = PathParser.getCacheStats();
      expect(stats.size).toBeLessThanOrEqual(stats.maxSize); // Should respect cache limits
    });

    it('should perform path operations efficiently', () => {
      const paths = [];
      
      // Generate test paths
      for (let i = 0; i < 1000; i++) {
        paths.push(`category${i % 10}.section${i % 5}.item${i}`);
      }

      const startTime = performance.now();
      
      // Perform various path operations
      for (const path of paths) {
        PathParser.getParent(path);
        PathParser.getLastSegment(path);
        PathParser.getDepth(path);
        PathParser.getAllParents(path);
        PathParser.isValid(path);
      }

      const operationTime = performance.now() - startTime;
      
      expect(operationTime).toBeLessThan(200); // Should complete operations quickly
    });
  });

  describe('JsonOperations Performance', () => {
    it('should handle large JSON objects efficiently', async () => {
      // Create a large nested JSON structure
      const largeObject: any = {};
      
      for (let category = 0; category < 50; category++) {
        largeObject[`category${category}`] = {};
        for (let item = 0; item < 100; item++) {
          largeObject[`category${category}`][`item${item}`] = {
            name: `Item ${category}-${item}`,
            description: `Description for item ${category}-${item}`,
            metadata: {
              created: new Date().toISOString(),
              version: 1,
              tags: [`tag${item % 10}`, `category${category}`]
            }
          };
        }
      }

      const filePath = join(tempDir, 'large.json');

      // Test write performance
      const writeStartTime = performance.now();
      await JsonOperations.writeFile(filePath, largeObject);
      const writeTime = performance.now() - writeStartTime;

      expect(writeTime).toBeLessThan(1000); // Should write large file quickly

      // Test read performance
      const readStartTime = performance.now();
      const result = await JsonOperations.parseFile(filePath);
      const readTime = performance.now() - readStartTime;

      expect(readTime).toBeLessThan(500); // Should read large file quickly
      expect(result.data).toEqual(largeObject);
    });

    it('should perform path operations efficiently on large objects', () => {
      // Create a large nested object
      const largeObject: any = {};
      for (let i = 0; i < 100; i++) {
        largeObject[`section${i}`] = {};
        for (let j = 0; j < 100; j++) {
          largeObject[`section${i}`][`item${j}`] = `value${i}-${j}`;
        }
      }

      const startTime = performance.now();
      
      // Perform many get operations
      for (let i = 0; i < 1000; i++) {
        const section = i % 100;
        const item = (i * 7) % 100; // Different pattern for variety
        JsonOperations.getValue(largeObject, `section${section}.item${item}`);
      }

      const getTime = performance.now() - startTime;
      
      expect(getTime).toBeLessThan(100); // Should perform gets quickly

      // Test set operations
      const setStartTime = performance.now();
      
      for (let i = 0; i < 1000; i++) {
        const section = i % 100;
        const item = i % 100;
        JsonOperations.setValue(largeObject, `section${section}.newItem${item}`, `newValue${i}`);
      }

      const setTime = performance.now() - setStartTime;
      
      expect(setTime).toBeLessThan(200); // Should perform sets quickly
    });

    it('should handle deep nesting efficiently', () => {
      // Create a very deeply nested structure
      let deepObject = {};
      let current = deepObject;
      
      // Create 100 levels of nesting
      for (let i = 0; i < 100; i++) {
        current[`level${i}`] = {};
        current = current[`level${i}`];
      }
      current['value'] = 'deep value';

      const deepPath = Array.from({ length: 100 }, (_, i) => `level${i}`).join('.') + '.value';

      const startTime = performance.now();
      
      // Access deep value many times
      for (let i = 0; i < 1000; i++) {
        JsonOperations.getValue(deepObject, deepPath);
      }

      const accessTime = performance.now() - startTime;
      
      expect(accessTime).toBeLessThan(200); // Should handle deep access efficiently
    });

    it('should perform getAllPaths efficiently', () => {
      // Create a moderately complex structure
      const complexObject: any = {};
      
      for (let category = 0; category < 20; category++) {
        complexObject[`category${category}`] = {};
        for (let section = 0; section < 10; section++) {
          complexObject[`category${category}`][`section${section}`] = {};
          for (let item = 0; item < 5; item++) {
            complexObject[`category${category}`][`section${section}`][`item${item}`] = `value${category}-${section}-${item}`;
          }
        }
      }

      const startTime = performance.now();
      const allPaths = JsonOperations.getAllPaths(complexObject);
      const pathTime = performance.now() - startTime;

      expect(pathTime).toBeLessThan(100); // Should extract all paths quickly
      expect(allPaths).toHaveLength(1000); // 20 * 10 * 5 = 1000 paths
    });
  });

  describe('FileWatcher Performance', () => {
    it('should handle multiple file changes efficiently', async () => {
      const index = new TranslationIndex({ baseLanguage: 'en' });
      const watcher = new TranslationFileWatcher({
        translationDir: tempDir,
        debounceMs: 10, // Faster for tests
        debug: false
      }, index);

      try {
        // Create multiple translation files
        const languages = ['en', 'es', 'fr', 'de', 'it'];
        const fileCount = languages.length;

        const startTime = performance.now();

        // Process files sequentially to measure performance
        for (const lang of languages) {
          const translations = {};
          for (let category = 0; category < 20; category++) {
            translations[`category${category}`] = {};
            for (let item = 0; item < 50; item++) {
              translations[`category${category}`][`item${item}`] = `${lang} value ${category}-${item}`;
            }
          }

          const filePath = join(tempDir, `${lang}.json`);
          await fs.writeFile(filePath, JSON.stringify(translations, null, 2));
          await watcher.processFile(filePath);
        }

        const processingTime = performance.now() - startTime;

        expect(processingTime).toBeLessThan(2000); // Should process all files quickly
        expect(index.getStats().totalKeys).toBe(1000); // 20 * 50 = 1000 keys
        expect(index.getStats().totalTranslations).toBe(5000); // 1000 keys * 5 languages

      } finally {
        await watcher.stop();
        watcher.removeAllListeners();
        index.removeAllListeners();
      }
    });
  });

  describe('Memory Efficiency', () => {
    it('should not leak memory during normal operations', async () => {
      const index = new TranslationIndex({ baseLanguage: 'en' });

      try {
        const initialMemory = process.memoryUsage().heapUsed;

        // Perform many operations
        for (let cycle = 0; cycle < 10; cycle++) {
          // Add translations
          for (let i = 0; i < 1000; i++) {
            index.set(`cycle${cycle}.item${i}`, 'en', `Value ${cycle}-${i}`);
          }

          // Search translations
          for (let i = 0; i < 100; i++) {
            await index.search(`cycle${cycle}`, { scope: 'keys', maxResults: 10 });
          }

          // Clear some translations
          for (let i = 0; i < 500; i++) {
            index.delete(`cycle${cycle}.item${i}`);
          }
        }

        // Force garbage collection if available
        if (global.gc) {
          global.gc();
        }

        const finalMemory = process.memoryUsage().heapUsed;
        const memoryIncrease = finalMemory - initialMemory;

        // Memory increase should be reasonable (less than 50MB for this workload)
        expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);

      } finally {
        index.removeAllListeners();
      }
    });
  });

  describe('Stress Tests', () => {
    it('should handle rapid concurrent operations', async () => {
      const index = new TranslationIndex({ baseLanguage: 'en' });

      try {
        const startTime = performance.now();

        // Create multiple concurrent operation streams
        const operations = [];

        // Stream 1: Add translations
        operations.push(
          (async () => {
            for (let i = 0; i < 1000; i++) {
              index.set(`stress.add${i}`, 'en', `Stress Add ${i}`);
              if (i % 100 === 0) await new Promise(resolve => setTimeout(resolve, 1));
            }
          })()
        );

        // Stream 2: Search translations
        operations.push(
          (async () => {
            for (let i = 0; i < 500; i++) {
              await index.search('stress', { scope: 'keys', maxResults: 10 });
              if (i % 50 === 0) await new Promise(resolve => setTimeout(resolve, 1));
            }
          })()
        );

        // Stream 3: Get context
        operations.push(
          (async () => {
            for (let i = 0; i < 500; i++) {
              if (index.get(`stress.add${i}`, 'en')) {
                await index.getContext(`stress.add${i}`, { depth: 1 });
              }
              if (i % 50 === 0) await new Promise(resolve => setTimeout(resolve, 1));
            }
          })()
        );

        // Stream 4: Batch operations
        operations.push(
          (async () => {
            for (let batch = 0; batch < 10; batch++) {
              const batchOps = [];
              for (let i = 0; i < 50; i++) {
                batchOps.push({
                  type: 'set' as const,
                  keyPath: `stress.batch${batch}.item${i}`,
                  language: 'en',
                  value: `Batch ${batch} Item ${i}`
                });
              }
              await index.batchUpdate(batchOps);
              await new Promise(resolve => setTimeout(resolve, 10));
            }
          })()
        );

        await Promise.all(operations);

        const totalTime = performance.now() - startTime;

        expect(totalTime).toBeLessThan(5000); // Should complete all concurrent operations quickly
        expect(index.getStats().totalKeys).toBeGreaterThan(1000);

      } finally {
        index.removeAllListeners();
      }
    });
  });
});
