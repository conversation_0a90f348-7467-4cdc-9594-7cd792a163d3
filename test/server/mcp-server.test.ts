/**
 * Unit tests for TranslationMCPServer
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { TranslationMCPServer } from '../../src/server/mcp-server.js';
import { ServerConfig } from '../../src/types/translation.js';

// Mock the MCP SDK
vi.mock('@modelcontextprotocol/sdk/server/mcp.js', () => {
  return {
    McpServer: vi.fn().mockImplementation(() => ({
      tool: vi.fn(),
      connect: vi.fn().mockResolvedValue(undefined)
    }))
  };
});

vi.mock('@modelcontextprotocol/sdk/server/stdio.js', () => {
  return {
    StdioServerTransport: vi.fn().mockImplementation(() => ({}))
  };
});

describe('TranslationMCPServer', () => {
  let tempDir: string;
  let server: TranslationMCPServer;
  let config: ServerConfig;

  beforeEach(async () => {
    tempDir = await globalThis.testUtils.createTempDir();
    await globalThis.testUtils.createTestTranslationFiles(tempDir);

    config = {
      name: 'test-i18n-mcp',
      version: '1.0.0',
      translationDir: tempDir,
      baseLanguage: 'en',
      debug: false,
      watchOptions: {
        debounceMs: 50,
        ignored: ['**/node_modules/**']
      }
    };
  });

  afterEach(async () => {
    if (server) {
      try {
        await server.stop();
      } catch (error) {
        // Ignore stop errors in tests
      }
      server.getIndex().removeAllListeners();
      server.getFileWatcher().removeAllListeners();
    }
    await globalThis.testUtils.cleanupTempDir(tempDir);
  });

  describe('constructor', () => {
    it('should initialize with provided configuration', () => {
      server = new TranslationMCPServer(config);
      
      expect(server).toBeDefined();
      expect(server.getIndex()).toBeDefined();
      expect(server.getFileWatcher()).toBeDefined();
    });

    it('should apply default configuration values', () => {
      const minimalConfig = {
        name: 'test-server',
        version: '1.0.0',
        translationDir: tempDir
      };
      
      server = new TranslationMCPServer(minimalConfig);
      expect(server).toBeDefined();
    });

    it('should setup event handlers', async () => {
      server = new TranslationMCPServer(config);
      
      const index = server.getIndex();
      const fileWatcher = server.getFileWatcher();
      
      // Verify event listeners are set up by checking listener count
      expect(index.listenerCount('set')).toBeGreaterThan(0);
      expect(index.listenerCount('delete')).toBeGreaterThan(0);
      expect(fileWatcher.listenerCount('fileProcessed')).toBeGreaterThan(0);
      expect(fileWatcher.listenerCount('error')).toBeGreaterThan(0);
    });
  });

  describe('MCP tool integration', () => {
    beforeEach(() => {
      server = new TranslationMCPServer(config);
    });

    it('should register search_translation tool', () => {
      const mockServer = server['server'];
      expect(mockServer.tool).toHaveBeenCalledWith(
        'search_translation',
        expect.any(Object),
        expect.any(Function)
      );
    });

    it('should register get_translation_context tool', () => {
      const mockServer = server['server'];
      expect(mockServer.tool).toHaveBeenCalledWith(
        'get_translation_context',
        expect.any(Object),
        expect.any(Function)
      );
    });

    it('should register update_translation tool', () => {
      const mockServer = server['server'];
      expect(mockServer.tool).toHaveBeenCalledWith(
        'update_translation',
        expect.any(Object),
        expect.any(Function)
      );
    });

    it('should register validate_structure tool', () => {
      const mockServer = server['server'];
      expect(mockServer.tool).toHaveBeenCalledWith(
        'validate_structure',
        expect.any(Object),
        expect.any(Function)
      );
    });

    it('should register get_stats tool', () => {
      const mockServer = server['server'];
      expect(mockServer.tool).toHaveBeenCalledWith(
        'get_stats',
        expect.any(Object),
        expect.any(Function)
      );
    });
  });

  describe('tool handlers', () => {
    beforeEach(async () => {
      server = new TranslationMCPServer(config);
      
      // Add some test data to the index
      const index = server.getIndex();
      index.set('common.buttons.submit', 'en', 'Submit');
      index.set('common.buttons.submit', 'es', 'Enviar');
      index.set('common.buttons.cancel', 'en', 'Cancel');
      index.set('auth.login.title', 'en', 'Login');
    });

    describe('search_translation handler', () => {
      it('should execute search and return formatted results', async () => {
        const mockServer = server['server'];
        const searchToolCall = mockServer.tool.mock.calls.find(
          call => call[0] === 'search_translation'
        );
        
        expect(searchToolCall).toBeDefined();
        const [, , handler] = searchToolCall;
        
        const result = await handler({
          query: 'submit',
          scope: 'both',
          maxResults: 10,
          caseSensitive: false
        });

        expect(result.content).toBeDefined();
        expect(result.content[0].type).toBe('text');
        
        const parsedResult = JSON.parse(result.content[0].text);
        expect(parsedResult.query).toBe('submit');
        expect(parsedResult.resultsCount).toBeGreaterThan(0);
        expect(parsedResult.results).toBeInstanceOf(Array);
      });

      it('should handle search errors gracefully', async () => {
        const mockServer = server['server'];
        const searchToolCall = mockServer.tool.mock.calls.find(
          call => call[0] === 'search_translation'
        );
        
        const [, , handler] = searchToolCall;
        
        // Mock the search method to throw an error
        const index = server.getIndex();
        vi.spyOn(index, 'search').mockRejectedValue(new Error('Search failed'));
        
        const result = await handler({
          query: 'test',
          scope: 'both'
        });

        expect(result.content[0].text).toContain('Error searching translations');
      });
    });

    describe('get_translation_context handler', () => {
      it('should get context and return formatted results', async () => {
        const mockServer = server['server'];
        const contextToolCall = mockServer.tool.mock.calls.find(
          call => call[0] === 'get_translation_context'
        );
        
        const [, , handler] = contextToolCall;
        
        const result = await handler({
          keyPath: 'common.buttons.submit',
          contextDepth: 1,
          languages: 'all'
        });

        expect(result.content[0].type).toBe('text');
        
        const parsedResult = JSON.parse(result.content[0].text);
        expect(parsedResult.keyPath).toBe('common.buttons.submit');
        expect(parsedResult.translations).toBeDefined();
      });

      it('should handle non-existent keys', async () => {
        const mockServer = server['server'];
        const contextToolCall = mockServer.tool.mock.calls.find(
          call => call[0] === 'get_translation_context'
        );
        
        const [, , handler] = contextToolCall;
        
        const result = await handler({
          keyPath: 'nonexistent.key',
          contextDepth: 1,
          languages: 'all'
        });

        expect(result.content[0].text).toContain('Translation key not found');
      });
    });

    describe('update_translation handler', () => {
      it('should update translations successfully', async () => {
        const mockServer = server['server'];
        const updateToolCall = mockServer.tool.mock.calls.find(
          call => call[0] === 'update_translation'
        );
        
        const [, , handler] = updateToolCall;
        
        const result = await handler({
          keyPath: 'common.buttons.submit',
          updates: {
            en: 'Updated Submit',
            fr: 'Soumettre'
          },
          validateStructure: false
        });

        expect(result.content[0].type).toBe('text');
        
        const parsedResult = JSON.parse(result.content[0].text);
        expect(parsedResult.success).toBe(true);
        expect(parsedResult.keyPath).toBe('common.buttons.submit');
        expect(parsedResult.updatedLanguages).toEqual(['en', 'fr']);
        
        // Verify the updates were applied
        const index = server.getIndex();
        expect(index.get('common.buttons.submit', 'en')?.value).toBe('Updated Submit');
        expect(index.get('common.buttons.submit', 'fr')?.value).toBe('Soumettre');
      });

      it('should validate structure when requested', async () => {
        const mockServer = server['server'];
        const updateToolCall = mockServer.tool.mock.calls.find(
          call => call[0] === 'update_translation'
        );
        
        const [, , handler] = updateToolCall;
        
        // Mock validation to return invalid
        const index = server.getIndex();
        vi.spyOn(index, 'validateStructure').mockResolvedValue({
          valid: false,
          missingKeys: {},
          extraKeys: {},
          typeMismatches: [],
          structuralIssues: ['Test validation failure']
        });
        
        const result = await handler({
          keyPath: 'test.key',
          updates: { en: 'value' },
          validateStructure: true
        });

        const parsedResult = JSON.parse(result.content[0].text);
        expect(parsedResult).toContain('Structure validation failed');
      });
    });

    describe('validate_structure handler', () => {
      it('should validate structure and return results', async () => {
        const mockServer = server['server'];
        const validateToolCall = mockServer.tool.mock.calls.find(
          call => call[0] === 'validate_structure'
        );
        
        const [, , handler] = validateToolCall;
        
        const result = await handler({
          baseLanguage: 'en',
          fix: false
        });

        expect(result.content[0].type).toBe('text');
        
        const parsedResult = JSON.parse(result.content[0].text);
        expect(parsedResult.valid).toBeTypeOf('boolean');
        expect(parsedResult.summary).toBeDefined();
        expect(parsedResult.details).toBeDefined();
      });

      it('should handle validation errors', async () => {
        const mockServer = server['server'];
        const validateToolCall = mockServer.tool.mock.calls.find(
          call => call[0] === 'validate_structure'
        );
        
        const [, , handler] = validateToolCall;
        
        // Mock validation to throw error
        const index = server.getIndex();
        vi.spyOn(index, 'validateStructure').mockRejectedValue(new Error('Validation error'));
        
        const result = await handler({
          baseLanguage: 'en',
          fix: false
        });

        expect(result.content[0].text).toContain('Error validating structure');
      });
    });

    describe('get_stats handler', () => {
      it('should return basic statistics', async () => {
        const mockServer = server['server'];
        const statsToolCall = mockServer.tool.mock.calls.find(
          call => call[0] === 'get_stats'
        );
        
        const [, , handler] = statsToolCall;
        
        const result = await handler({
          includeDetails: false
        });

        expect(result.content[0].type).toBe('text');
        
        const parsedResult = JSON.parse(result.content[0].text);
        expect(parsedResult.server).toBeDefined();
        expect(parsedResult.index).toBeDefined();
        expect(parsedResult.watcher).toBeDefined();
        expect(parsedResult.server.name).toBe('test-i18n-mcp');
      });

      it('should return detailed statistics when requested', async () => {
        const mockServer = server['server'];
        const statsToolCall = mockServer.tool.mock.calls.find(
          call => call[0] === 'get_stats'
        );
        
        const [, , handler] = statsToolCall;
        
        const result = await handler({
          includeDetails: true
        });

        const parsedResult = JSON.parse(result.content[0].text);
        expect(parsedResult.details).toBeDefined();
        expect(parsedResult.details.allLanguages).toBeDefined();
        expect(parsedResult.details.sampleKeys).toBeDefined();
      });
    });
  });

  describe('start and stop', () => {
    beforeEach(() => {
      server = new TranslationMCPServer(config);
    });

    it('should start server successfully', async () => {
      // Mock the file watcher start method
      const fileWatcher = server.getFileWatcher();
      vi.spyOn(fileWatcher, 'start').mockResolvedValue();

      await server.start();

      expect(fileWatcher.start).toHaveBeenCalled();
      
      const mockServer = server['server'];
      expect(mockServer.connect).toHaveBeenCalled();
    });

    it('should handle start errors', async () => {
      const fileWatcher = server.getFileWatcher();
      vi.spyOn(fileWatcher, 'start').mockRejectedValue(new Error('Watcher start failed'));

      await expect(server.start()).rejects.toThrow('Watcher start failed');
    });

    it('should stop server successfully', async () => {
      const fileWatcher = server.getFileWatcher();
      vi.spyOn(fileWatcher, 'start').mockResolvedValue();
      vi.spyOn(fileWatcher, 'stop').mockResolvedValue();

      await server.start();
      await server.stop();

      expect(fileWatcher.stop).toHaveBeenCalled();
    });

    it('should handle stop errors', async () => {
      const fileWatcher = server.getFileWatcher();
      vi.spyOn(fileWatcher, 'stop').mockRejectedValue(new Error('Stop failed'));

      await expect(server.stop()).rejects.toThrow('Stop failed');
    });
  });

  describe('event handling', () => {
    beforeEach(() => {
      server = new TranslationMCPServer({ ...config, debug: true });
    });

    it('should handle index set events in debug mode', () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
      
      const index = server.getIndex();
      index.set('test.key', 'en', 'Test Value');

      // In debug mode, should log the event
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Translation set: test.key [en]')
      );
    });

    it('should handle index delete events in debug mode', () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
      
      const index = server.getIndex();
      index.set('test.key', 'en', 'Test Value');
      index.delete('test.key', 'en');

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Translation deleted: test.key [en]')
      );
    });

    it('should handle file watcher events', () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
      const errorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      const fileWatcher = server.getFileWatcher();
      
      // Simulate file processed event
      fileWatcher.emit('fileProcessed', {
        type: 'add',
        path: '/test/path.json',
        language: 'en',
        timestamp: Date.now()
      });

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('File processed: add /test/path.json [en]')
      );

      // Simulate error event
      fileWatcher.emit('error', new Error('Test error'));

      expect(errorSpy).toHaveBeenCalledWith(
        expect.stringContaining('File watcher error:'),
        expect.any(Error)
      );

      // Simulate ready event
      fileWatcher.emit('ready');

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('File watcher ready')
      );
    });
  });

  describe('getIndex and getFileWatcher', () => {
    beforeEach(() => {
      server = new TranslationMCPServer(config);
    });

    it('should provide access to underlying index', () => {
      const index = server.getIndex();
      expect(index).toBeDefined();
      expect(typeof index.set).toBe('function');
      expect(typeof index.get).toBe('function');
      expect(typeof index.search).toBe('function');
    });

    it('should provide access to underlying file watcher', () => {
      const fileWatcher = server.getFileWatcher();
      expect(fileWatcher).toBeDefined();
      expect(typeof fileWatcher.start).toBe('function');
      expect(typeof fileWatcher.stop).toBe('function');
      expect(typeof fileWatcher.getStats).toBe('function');
    });
  });

  describe('configuration handling', () => {
    it('should handle minimal configuration', () => {
      const minimalConfig = {
        name: 'minimal-server',
        version: '1.0.0',
        translationDir: tempDir
      };

      server = new TranslationMCPServer(minimalConfig);
      expect(server).toBeDefined();
    });

    it('should apply custom watch options', () => {
      const customConfig = {
        ...config,
        watchOptions: {
          debounceMs: 200,
          ignored: ['**/custom/**', '**/ignored/**']
        }
      };

      server = new TranslationMCPServer(customConfig);
      expect(server).toBeDefined();
    });

    it('should handle custom base language', () => {
      const customConfig = {
        ...config,
        baseLanguage: 'es'
      };

      server = new TranslationMCPServer(customConfig);
      const index = server.getIndex();
      
      // Base language should be set correctly
      expect(index.getLanguages()).toEqual(expect.arrayContaining([]));
    });
  });
});
