/**
 * Tests for the translation integrity checker tool
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { join } from 'path';
import { promises as fs } from 'fs';
import { TranslationIntegrityChecker } from '../../src/tools/check-translation-integrity.js';
import { ServerConfig } from '../../src/types/translation.js';

describe('TranslationIntegrityChecker', () => {
  const testFixturesDir = join(process.cwd(), 'test', 'fixtures');
  const tempDir = join(process.cwd(), 'test', 'temp-integrity');
  
  const config: Required<ServerConfig> = {
    name: 'test-server',
    version: '1.0.0',
    translationDir: testFixturesDir,
    baseLanguage: 'en',
    debug: false,
    watchOptions: {
      debounceMs: 100,
      ignored: []
    },
    srcDir: '',
    exclude: [],
    autoSync: false,
    generateTypes: '',
    watchCode: false,
    projectRoot: '',
    frameworks: [],
    keyStyle: 'nested'
  };

  beforeEach(async () => {
    // Create temp directory for test files
    await fs.mkdir(tempDir, { recursive: true });
  });

  afterEach(async () => {
    // Clean up temp directory
    try {
      await fs.rm(tempDir, { recursive: true, force: true });
    } catch (error) {
      // Ignore cleanup errors
    }
  });

  describe('checkIntegrity', () => {
    it('should detect missing keys in French translation', async () => {
      const checker = new TranslationIntegrityChecker(config);
      
      const result = await checker.checkIntegrity({
        baseLanguage: 'en',
        includeDetails: true,
        onlyShowIssues: false,
        checkTypes: true
      });

      expect(result.isValid).toBe(false);
      expect(result.baseLanguage).toBe('en');
      expect(result.totalFiles).toBe(3); // en.json, es.json, fr.json

      // Check summary statistics
      expect(result.summary.filesWithIssues).toBeGreaterThan(0);
      expect(result.summary.totalMissingKeys).toBeGreaterThan(0);

      // Check French file specifically (should have missing keys)
      expect(result.fileResults.fr).toBeDefined();
      const frResult = result.fileResults.fr;
      
      expect(frResult.validJson).toBe(true);
      expect(frResult.stats.missingKeys).toBeGreaterThan(0);
      expect(frResult.missingKeys.length).toBeGreaterThan(0);

      // Should detect missing auth section
      const missingAuthKeys = frResult.missingKeys.filter(k => k.keyPath.startsWith('auth.'));
      expect(missingAuthKeys.length).toBeGreaterThan(0);

      // Should detect missing button keys
      const missingButtonKeys = frResult.missingKeys.filter(k => 
        k.keyPath === 'common.buttons.delete' || k.keyPath === 'common.buttons.edit'
      );
      expect(missingButtonKeys.length).toBe(2);
    });

    it('should show Spanish translation as complete', async () => {
      const checker = new TranslationIntegrityChecker(config);
      
      const result = await checker.checkIntegrity({
        baseLanguage: 'en',
        includeDetails: true,
        onlyShowIssues: false,
        checkTypes: true
      });

      // Check Spanish file (should be complete)
      expect(result.fileResults.es).toBeDefined();
      const esResult = result.fileResults.es;
      
      expect(esResult.validJson).toBe(true);
      expect(esResult.stats.missingKeys).toBe(0);
      expect(esResult.stats.extraKeys).toBe(0);
      expect(esResult.stats.completeness).toBe(1); // 100% complete
      expect(esResult.missingKeys.length).toBe(0);
      expect(esResult.extraKeys.length).toBe(0);
    });

    it('should filter results when onlyShowIssues is true', async () => {
      const checker = new TranslationIntegrityChecker(config);
      
      const result = await checker.checkIntegrity({
        baseLanguage: 'en',
        includeDetails: true,
        onlyShowIssues: true,
        checkTypes: true
      });

      // Should only show files with issues (fr.json)
      expect(result.fileResults.fr).toBeDefined(); // Has missing keys
      expect(result.fileResults.es).toBeUndefined(); // Complete, should be filtered out
    });

    it('should provide meaningful recommendations', async () => {
      const checker = new TranslationIntegrityChecker(config);
      
      const result = await checker.checkIntegrity({
        baseLanguage: 'en',
        includeDetails: true,
        onlyShowIssues: false,
        checkTypes: true
      });

      // Global recommendations should exist
      expect(result.recommendations).toBeDefined();
      expect(result.recommendations.length).toBeGreaterThan(0);

      // Should have recommendations about missing keys
      const missingKeysRec = result.recommendations.find(r => r.includes('missing translation'));
      expect(missingKeysRec).toBeDefined();

      // French file should have specific recommendations
      const frResult = result.fileResults.fr;
      expect(frResult.recommendations).toBeDefined();
      expect(frResult.recommendations.length).toBeGreaterThan(0);
      
      const addMissingRec = frResult.recommendations.find(r => r.includes('Add') && r.includes('missing'));
      expect(addMissingRec).toBeDefined();
    });

    it('should handle invalid JSON files gracefully', async () => {
      // Create a test config pointing to temp directory
      const tempConfig = { ...config, translationDir: tempDir };
      
      // Create valid base file
      await fs.writeFile(
        join(tempDir, 'en.json'),
        JSON.stringify({ test: { key: "value" } }, null, 2)
      );
      
      // Create invalid JSON file
      await fs.writeFile(
        join(tempDir, 'invalid.json'),
        '{ "test": { "key": "value" }'  // Missing closing brace
      );

      const checker = new TranslationIntegrityChecker(tempConfig);
      
      const result = await checker.checkIntegrity({
        baseLanguage: 'en',
        includeDetails: true,
        onlyShowIssues: false,
        checkTypes: true
      });

      expect(result.fileResults.invalid).toBeDefined();
      const invalidResult = result.fileResults.invalid;
      
      expect(invalidResult.validJson).toBe(false);
      expect(invalidResult.parseError).toBeDefined();
      expect(invalidResult.recommendations[0]).toContain('Fix JSON syntax errors');
    });

    it('should detect type mismatches when enabled', async () => {
      // Create test files with type mismatches
      const tempConfig = { ...config, translationDir: tempDir };
      
      await fs.writeFile(
        join(tempDir, 'en.json'),
        JSON.stringify({ 
          test: { 
            stringValue: "hello",
            numberValue: 42,
            boolValue: true
          } 
        }, null, 2)
      );
      
      await fs.writeFile(
        join(tempDir, 'mismatch.json'),
        JSON.stringify({ 
          test: { 
            stringValue: "hello",
            numberValue: "42",  // Should be number
            boolValue: "true"   // Should be boolean
          } 
        }, null, 2)
      );

      const checker = new TranslationIntegrityChecker(tempConfig);
      
      const result = await checker.checkIntegrity({
        baseLanguage: 'en',
        includeDetails: true,
        onlyShowIssues: false,
        checkTypes: true
      });

      expect(result.fileResults.mismatch).toBeDefined();
      const mismatchResult = result.fileResults.mismatch;
      
      expect(mismatchResult.stats.typeMismatches).toBe(2);
      expect(mismatchResult.typeMismatches.length).toBe(2);
      
      // Check specific type mismatches
      const numberMismatch = mismatchResult.typeMismatches.find(m => m.keyPath === 'test.numberValue');
      expect(numberMismatch).toBeDefined();
      expect(numberMismatch?.expectedType).toBe('number');
      expect(numberMismatch?.actualType).toBe('string');
      
      const boolMismatch = mismatchResult.typeMismatches.find(m => m.keyPath === 'test.boolValue');
      expect(boolMismatch).toBeDefined();
      expect(boolMismatch?.expectedType).toBe('boolean');
      expect(boolMismatch?.actualType).toBe('string');
    });

    it('should calculate completeness percentage correctly', async () => {
      const checker = new TranslationIntegrityChecker(config);
      
      const result = await checker.checkIntegrity({
        baseLanguage: 'en',
        includeDetails: true,
        onlyShowIssues: false,
        checkTypes: true
      });

      const frResult = result.fileResults.fr;
      
      // French file is missing several keys, so completeness should be less than 100%
      expect(frResult.stats.completeness).toBeLessThan(1);
      expect(frResult.stats.completeness).toBeGreaterThan(0);
      
      // Completeness should be calculated as (total - missing) / total
      const expectedCompleteness = (frResult.stats.totalKeys) / (frResult.stats.totalKeys + frResult.stats.missingKeys);
      expect(Math.abs(frResult.stats.completeness - expectedCompleteness)).toBeLessThan(0.01);
    });
  });
});
