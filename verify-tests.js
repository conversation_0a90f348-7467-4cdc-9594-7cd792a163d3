#!/usr/bin/env node

/**
 * Quick test verification script
 * Runs a subset of tests to verify the test suite is working correctly
 */

import { spawn } from 'child_process';
import { existsSync } from 'fs';
import { join } from 'path';

const COLORS = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  bright: '\x1b[1m'
};

const log = (color, message) => {
  console.log(`${COLORS[color]}${message}${COLORS.reset}`);
};

const runCommand = (command, args = []) => {
  return new Promise((resolve, reject) => {
    const child = spawn(command, args, {
      stdio: 'pipe',
      shell: true
    });

    let stdout = '';
    let stderr = '';

    child.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    child.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    child.on('close', (code) => {
      resolve({ code, stdout, stderr });
    });

    child.on('error', (error) => {
      reject(error);
    });
  });
};

async function main() {
  log('bright', '🧪 i18n MCP Server - Test Suite Verification');
  console.log('');

  // Check if we're in the right directory
  if (!existsSync('package.json')) {
    log('red', '❌ package.json not found. Please run from project root.');
    process.exit(1);
  }

  if (!existsSync('node_modules')) {
    log('yellow', '⚠️  node_modules not found. Installing dependencies...');
    const { code } = await runCommand('npm', ['install']);
    if (code !== 0) {
      log('red', '❌ Failed to install dependencies');
      process.exit(1);
    }
  }

  const tests = [
    {
      name: 'TypeScript Compilation',
      command: 'npm',
      args: ['run', 'build'],
      required: true
    },
    {
      name: 'Path Parser Utils',
      command: 'npm',
      args: ['run', 'test:run', '--', 'test/utils/path-parser.test.ts'],
      required: true
    },
    {
      name: 'JSON Operations Utils',
      command: 'npm',
      args: ['run', 'test:run', '--', 'test/utils/json-operations.test.ts'],
      required: true
    },
    {
      name: 'Translation Index Core',
      command: 'npm',
      args: ['run', 'test:run', '--', 'test/core/translation-index.test.ts'],
      required: true
    },
    {
      name: 'Error Types',
      command: 'npm',
      args: ['run', 'test:run', '--', 'test/utils/error-types.test.ts'],
      required: false
    }
  ];

  let passed = 0;
  let failed = 0;

  for (const test of tests) {
    process.stdout.write(`${COLORS.blue}Running ${test.name}...${COLORS.reset}`);
    
    try {
      const { code, stderr } = await runCommand(test.command, test.args);
      
      if (code === 0) {
        log('green', ' ✅ PASSED');
        passed++;
      } else {
        log('red', ' ❌ FAILED');
        if (stderr) {
          console.log(stderr.slice(0, 200) + '...');
        }
        failed++;
        
        if (test.required) {
          log('red', `❌ Required test failed: ${test.name}`);
          process.exit(1);
        }
      }
    } catch (error) {
      log('red', ` ❌ ERROR: ${error.message}`);
      failed++;
      
      if (test.required) {
        log('red', `❌ Required test failed: ${test.name}`);
        process.exit(1);
      }
    }
  }

  console.log('');
  log('bright', '📊 Test Summary:');
  log('green', `✅ Passed: ${passed}`);
  if (failed > 0) {
    log('red', `❌ Failed: ${failed}`);
  }

  if (failed === 0) {
    log('green', '🎉 All verification tests passed!');
    console.log('');
    log('bright', 'Next steps:');
    console.log('  • Run full test suite: npm test');
    console.log('  • Run with coverage: npm run test:coverage');
    console.log('  • Use test runner: node test-runner.js help');
    console.log('');
  } else {
    log('yellow', '⚠️  Some tests failed, but core functionality is working');
    console.log('');
  }

  // Quick file structure check
  log('bright', '📁 Test Structure Verification:');
  const expectedFiles = [
    'test/setup.ts',
    'test/test-utils.ts',
    'test/utils/path-parser.test.ts',
    'test/utils/json-operations.test.ts',
    'test/core/translation-index.test.ts',
    'test/core/file-watcher.test.ts',
    'test/server/mcp-server.test.ts',
    'test/integration/end-to-end.test.ts',
    'test/performance/benchmarks.test.ts',
    'vitest.config.ts'
  ];

  let filesOk = 0;
  for (const file of expectedFiles) {
    if (existsSync(file)) {
      log('green', `✅ ${file}`);
      filesOk++;
    } else {
      log('red', `❌ ${file} (missing)`);
    }
  }

  console.log('');
  log('bright', `📋 File Check: ${filesOk}/${expectedFiles.length} files present`);

  if (filesOk === expectedFiles.length) {
    log('green', '🎯 Test suite is properly set up!');
  }

  console.log('');
  log('bright', '📖 For detailed testing information, see: test/README.md');
}

main().catch((error) => {
  log('red', `💥 Fatal error: ${error.message}`);
  process.exit(1);
});
